import { AddressInput } from './address.js';
import Case from './case.js';
import FulfillmentMeta from './fulfillmentMeta.js';
import Fund from './fund.js';
import { BankAccount } from './payee.js';
import { Payment, PaymentMethod } from './payment.js';
import { PaymentPattern } from './paymentPattern.js';
import WorkflowEvent from './workflowEvent.js';

export interface FulfillmentMetadata {
  authorizedDate?: Date;
}

export interface Fulfillment {
  id: string;
  displayId: string;
  legacyId?: string;
  caseId: string;
  case: Case;
  fundId: string;
  fund: Fund;
  fulfillmentMeta?: FulfillmentMeta;
  payments?: Payment[];
  metadata: FulfillmentMetadata;
  approvedAmount: number;
  scheduleType: ScheduleType;
  paymentPattern?: PaymentPattern;
  schedule?: Date[];
  deactivatedAt?: Date;
  workflowEvents?: WorkflowEvent[];
}
export interface FulfillmentFilter {
  ids?: string[];
}

export default Fulfillment;
export interface ClaimDirectDepositRequest {
  id: string;
  bankAccount: BankAccount;
}
export interface ClaimCheckRequest {
  id: string;
  address: AddressInput;
}
export interface ClaimPhysicalCardRequest {
  id: string;
  address: AddressInput;
}
export interface ClaimVirtualCardRequest {
  id: string;
  address: AddressInput;
}

export interface ClaimZelleRequest {
  id: string;
  email?: string;
}

export type ClaimRequest =
  | ClaimDirectDepositRequest
  | ClaimCheckRequest
  | ClaimPhysicalCardRequest
  | ClaimVirtualCardRequest
  | ClaimZelleRequest;

export type ClaimFundsInput = ClaimRequest & {
  paymentMethod: PaymentMethod;
};

export interface IssueFundsInput {
  id: string;
}

export enum ScheduleType {
  OneTime = 'onetime',
  Recurring = 'recurring',
}

export interface ApproveAmountInput {
  id: string;
}
export interface BulkIssueFundsInput {
  ids: string[];
}
