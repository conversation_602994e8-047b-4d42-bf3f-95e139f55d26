import {
  ApproveCasePaymentsInput,
  ExpediteCasesInput,
  TransitionCaseStatusesInput,
} from './case.js';
import { CreateBulkProgramReferralInput } from './programReferral.js';
import { BulkIssueFundsInput } from './fulfillment.js';

export const BULK_PAYMENT_ACTION_LIMIT = 50;

export enum BulkOperationType {
  ApproveCasePayments = 'ApproveCasePayments',
  AddComment = 'AddComment',
  ExpediteApplications = 'ExpediteApplications',
  OverrideCasePayments = 'OverrideCasePayments',
  TransitionCaseStatuses = 'TransitionCaseStatuses',
  UndoExpediteApplications = 'UndoExpediteApplications',
  ProgramReferrals = 'ProgramReferrals',
  IssueFunds = 'IssueFunds',
}

export enum BulkOperationStatus {
  Validating = 'Validating',
  ValidationFailed = 'ValidationFailed',
  InProgress = 'InProgress',
  Failed = 'Failed',
  Completed = 'Completed',
}

export interface BulkOperation {
  id: string;
  operation: BulkOperationType;
  status: BulkOperationStatus;
  payload:
    | ApproveCasePaymentsInput
    | ExpediteCasesInput
    | TransitionCaseStatusesInput
    | CreateBulkProgramReferralInput
    | BulkIssueFundsInput;
  adminId: string;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
}
