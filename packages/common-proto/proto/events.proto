syntax = "proto3";

// New domain-level events. This is the preferred event structure, please
// add new event types to this file.
package events;

// Could add more data from token here
message Actor {
  string id = 1;
}

enum PaymentEventType {
  PAYMENT_UNKNOWN = 0;
  PAYMENT_CREATED = 1;
  PAYMENT_COMPLETED = 2;
  PAYMENT_FAILED = 3;
  PAYMENT_CANCELED = 4;
  PAYMENT_UPDATED = 5;
  PAYMENT_DELETED = 6;
  PAYMENT_RESET = 7;
  PAYMENT_INITIATED = 8;
  PAYMENT_TRANSACTION_REQUEST = 9;
}

message PaymentDetails {
  string reason = 1;
}

message PaymentEvent {
  string payment_id = 1;
  string fulfillment_id = 2;
  string case_id = 3;
  string application_id = 4;
  string timestamp = 5;
  PaymentEventType event_type = 6;
  optional Actor actor = 7;
  optional PaymentDetails details = 8;
}
