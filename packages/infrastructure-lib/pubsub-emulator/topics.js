const hosts = {
  doctopus: 'http://localhost:12001',
  grpc: 'http://localhost:7001',
  payments: 'http://localhost:10012/v1/subscriptions',
  platform: 'http://localhost:10010/subscriptions',
};

const topics = [
  {
    name: 'document-upload-local',
    subscriptions: {
      push: [{ name: 'document-upload-local-push-whatsup', endpoint: `${hosts.doctopus}/whatsup` }],
      pull: [],
    },
  },
  {
    name: 'application-answers-updated-local',
    subscriptions: {
      push: [],
      pull: [
        {
          name: 'application-answers-updated-elasticsearch-sync-pull-local',
        },
      ],
    },
  },
  {
    name: 'application-started-local',
    subscriptions: {
      push: [],
      pull: [
        {
          name: 'application-started-elasticsearch-sync-pull-local',
        },
      ],
    },
  },
  {
    name: 'application-submission-local',
    subscriptions: {
      push: [
        {
          name: 'application-submission-local-push-grpc',
          endpoint: `${hosts.grpc}/applicationSubmitted`,
        },
      ],
      pull: [
        {
          name: 'application-submission-elasticsearch-sync-pull-local',
        },
      ],
    },
  },
  {
    name: 'application-version-creation-local',
    subscriptions: {
      push: [
        {
          name: 'application-version-creation-local-push-grpc',
          endpoint: `${hosts.grpc}/applicationVersionCreated`,
        },
      ],
      pull: [
        {
          name: 'application-version-creation-elasticsearch-sync-pull-local',
        },
      ],
    },
  },
  {
    name: 'case-reassigned-local',
    subscriptions: {
      push: [],
      pull: [
        {
          name: 'case-reassigned-elasticsearch-sync-pull-local',
        },
      ],
    },
  },
  {
    name: 'case-status-changed-local',
    subscriptions: {
      push: [],
      pull: [
        {
          name: 'case-status-changed-elasticsearch-sync-pull-local',
        },
      ],
    },
  },
  {
    name: 'case-tag-added-local',
    subscriptions: {
      push: [],
      pull: [
        {
          name: 'case-tag-added-elasticsearch-sync-pull-local',
        },
      ],
    },
  },
  {
    name: 'case-tag-removed-local',
    subscriptions: {
      push: [],
      pull: [
        {
          name: 'case-tag-removed-elasticsearch-sync-pull-local',
        },
      ],
    },
  },
  {
    name: 'case-participant-link-local',
    subscriptions: {
      push: [
        {
          name: 'case-participant-link-local-push-platform',
          endpoint: `${hosts.platform}/caseParticipantLinked`,
        },
      ],
      pull: [],
    },
  },
  {
    name: 'case-participant-unlink-local',
    subscriptions: {
      push: [
        {
          name: 'case-participant-unlink-local-push-platform',
          endpoint: `${hosts.platform}/caseParticipantUnlinked`,
        },
      ],
      pull: [],
    },
  },
  {
    name: 'payments-local',
    subscriptions: {
      push: [
        {
          name: 'payment-processor-local-push-payments',
          endpoint: `${hosts.payments}/processPayment`,
        },
      ],
      pull: [
        {
          name: 'payments-elasticsearch-sync-pull-local',
        },
      ],
    },
  },
  {
    name: 'scheduled-payments-local',
    subscriptions: {
      push: [
        {
          name: 'scheduled-payments-local-push-payments',
          endpoint: `${hosts.payments}/scheduledPayments`,
        },
      ],
      pull: [],
    },
    wrapped: true,
  },
  {
    name: 'scheduled-transfers-local',
    subscriptions: {
      push: [
        {
          name: 'scheduled-transfers-local-push-payments',
          endpoint: `${hosts.payments}/transfers`,
        },
      ],
      pull: [],
    },
    wrapped: true,
  },
];

export default topics;
