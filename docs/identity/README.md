# Beam Identity & Authorization System

Welcome to the Beam Identity documentation! This system handles authentication, authorization, and user management across all Beam services using Google Identity Platform (GCIP) and SpiceDB.

*You're probably asking yourself: How did I get here? This is not my beautiful house? This is not my beautiful wife!? Do not fret, engineer. I'm here to tell you that this is, in fact, that large automobile you've been looking for. This must be the place.*

## 🏗️ Architecture Overview

Beam's identity system consists of three main components:

- **Google Identity Platform (GCIP)** - Multi-tenant authentication with Firebase Auth
- **SpiceDB** - Relationship-based authorization (ReBAC) 
- **Identity Service** - Core service that orchestrates authentication and authorization

## 📚 Documentation Index

### Core Concepts
- [**Database Schema & User Records**](./identity-db.md) - Database tables, relationships, and user lifecycle
- [**Permission & Tenant Management**](./permission-tenant.md) - SpiceDB relationships and GCIP tenant overview

### Authentication & Token Management
- [**GCIP Token Management**](./gcip-token-management.md) - How tokens are issued, verified, and refreshed
- [**GCIP User Lifecycle**](./gcip-user-lifecycle.md) - User creation, provisioning, and deactivation flows

### Authorization
- [**SpiceDB Schema**](./spicedb-schema.md) - Complete authorization schema reference
- [**SpiceDB Relationships**](./spicedb-relationships.md) - How to create and manage authorization relationships

### API Reference
- [**Identity Client Protocol**](./identity-client-proto.md) - Complete gRPC API documentation

### Application Integration
- [**Partner Portal Auth**](./partner-portal-auth.md) - How partner-portal implements authentication
- [**Platform UI Auth**](./platform-ui-auth.md) - How platform-ui (applicant portal) handles auth

### Operations & Disaster Recovery
- [**GCIP Disaster Recovery Options**](./gcip-disaster-recovery-options.md) - Comprehensive disaster recovery analysis and recommendations

## 🚀 Quick Start

### For Developers
1. Start with [Database Schema](./identity-db.md) to understand the data model
2. Review [Permission & Tenant Management](./permission-tenant.md) for high-level concepts
3. Check [Identity Client Protocol](./identity-client-proto.md) for API usage

### For Frontend Developers
1. Read [Partner Portal Auth](./partner-portal-auth.md) for partner-facing apps
2. Read [Platform UI Auth](./platform-ui-auth.md) for applicant-facing apps
3. Understand [GCIP Token Management](./gcip-token-management.md) for token handling

### For Backend Developers
1. Review [SpiceDB Schema](./spicedb-schema.md) for authorization rules
2. Learn [SpiceDB Relationships](./spicedb-relationships.md) for permission management
3. Study [GCIP User Lifecycle](./gcip-user-lifecycle.md) for user management

### For Operations/SRE
1. Review [GCIP Disaster Recovery Options](./gcip-disaster-recovery-options.md) for disaster planning
2. Understand [GCIP Token Management](./gcip-token-management.md) for troubleshooting auth issues
3. Study [Database Schema](./identity-db.md) for operational monitoring

## 🔧 Common Tasks

| Task | Documentation |
|------|---------------|
| Add new permissions and relations | [SpiceDB Schema](./spicedb-schema.md) |
| Create authorization relationships | [SpiceDB Relationships](./spicedb-relationships.md) |
| Integrate authentication in a new service | [Identity Client Protocol](./identity-client-proto.md) |
| Set up SAML/SSO for a partner | [Permission & Tenant Management](./permission-tenant.md) |
| Debug authentication issues | [GCIP Token Management](./gcip-token-management.md) |
| Understand user data flow | [GCIP User Lifecycle](./gcip-user-lifecycle.md) |
| Plan disaster recovery | [GCIP Disaster Recovery Options](./gcip-disaster-recovery-options.md) |
| Troubleshoot partner portal auth | [Partner Portal Auth](./partner-portal-auth.md) |
| Troubleshoot applicant portal auth | [Platform UI Auth](./platform-ui-auth.md) |

## 🛠️ Development Environment

To work with the identity system locally:

1. Start the required databases:
   ```bash
   pnpm start:databases -d
   ```

2. Start the emulators:
   ```bash
   pnpm start:emulators
   ```

3. Seed the emulators with test data:
   ```bash
   pnpm seed:emulators
   ```

4. Run the identity service:
   ```bash
   turbo start:dev -F @bybeam/identity-server
   ```

5. See individual documentation files for specific setup instructions and examples.

## 🚨 Emergency Procedures

For production incidents involving identity services:

1. **Authentication failures**: Check [GCIP Token Management](./gcip-token-management.md) for troubleshooting steps
2. **Authorization issues**: Review [SpiceDB Relationships](./spicedb-relationships.md) for permission debugging
3. **GCIP outages**: Follow procedures in [GCIP Disaster Recovery Options](./gcip-disaster-recovery-options.md)
4. **Database issues**: Refer to [Database Schema](./identity-db.md) for data recovery guidance

## 📖 External Resources

- [SpiceDB Documentation](https://authzed.com/docs)
- [Firebase Auth Documentation](https://firebase.google.com/docs/auth)
- [Google Identity Platform](https://cloud.google.com/identity-platform/docs)

---

*Last updated: 2025-07-03*

*© Claude & Charlie*

*Same as it ever was*

*All references to Talking Heads lyrics are purely coincidental and bubbled to the top of the author's unconscious.*