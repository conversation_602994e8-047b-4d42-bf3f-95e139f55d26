# GCIP Disaster Recovery Options Analysis

## Executive Summary

This document analyzes disaster recovery options for Google Cloud Identity Platform (GCIP) outages affecting Beam's identity services. The primary challenge is ensuring business continuity when GCIP becomes unavailable, requiring a comprehensive data backup and failover strategy.

**Key Findings:**
- Current multi-region deployment lacks data synchronization capabilities
- Critical data spans 3 systems: Identity Database, SpiceDB, and external GCIP state
- Dual-region writes offer better consistency but higher complexity
- Background sync provides simpler implementation with acceptable recovery time
- **Recommended approach**: Hybrid strategy combining database-level replication with application-layer GCIP backup

---

## Current State Analysis

### Architecture Overview
- **Multi-region deployment**: us-central1 (primary) and us-west1 (secondary)
- **Single GCIP project**: `core-platform-prod-beam` shared across regions
- **No cross-region data sync**: Each region operates independently
- **Missing GCIP backup**: No mechanism to replicate Firebase/GCIP state

### Data Dependencies
1. **Identity Database** (`dbs/identity-db`): User accounts, GCIP mappings, role assignments
2. **Core Database** (`dbs/core-db`): Partner/program context, application data
3. **SpiceDB**: Authorization relationships and permissions
4. **GCIP External State**: Authentication records, tenant configs, custom claims

---

## Critical Data Backup Requirements

### High-Priority Data (Real-time sync required)
- **User accounts** and GCIP UID mappings
- **Active sessions** and authentication state
- **Role assignments** (applicant/advocate relationships)
- **SpiceDB relationships** for authorization
- **Partner tenant configurations**

### Medium-Priority Data (Near real-time acceptable)
- **Audit logs** and security events
- **Access request workflows**
- **Historical session data**

### GCIP External State (Cannot be replicated directly)
- **Tenant configurations** per partner
- **Identity provider settings** (SAML/OIDC)
- **Custom claims** and security rules
- **User authentication records**

---

## Approach 1: Dual-Region Writes

### Implementation Requirements

#### Database Layer Changes
```typescript
// Example implementation in identity service
class UserService {
  async createUser(userData: CreateUserData) {
    const results = await Promise.allSettled([
      this.centralRegionDB.users.create(userData),
      this.westRegionDB.users.create(userData)
    ]);
    
    return this.handleDualWriteResults(results);
  }
}
```

#### Infrastructure Requirements
- **Separate GCIP projects** for each region
- **Cross-region database connections** from both identity servers
- **Transaction coordinator** for distributed writes
- **Conflict resolution mechanism** for simultaneous updates
- **Health monitoring** for each region's write success

#### SpiceDB Synchronization
- **Dual SpiceDB instances** with relationship mirroring
- **Write-through pattern** for all permission changes
- **Consistency verification** jobs

### Pros
✅ **Near-zero data loss** in disaster scenarios  
✅ **Immediate failover capability** with consistent state  
✅ **Real-time data consistency** across regions  
✅ **Simpler recovery procedures** (just redirect traffic)

### Cons
❌ **High complexity** in application code  
❌ **Performance impact** from dual writes  
❌ **Increased failure modes** (partial write failures)  
❌ **Significant development effort** (3-6 months)  
❌ **Higher operational costs** (dual infrastructure)  
❌ **Complex transaction management** across regions

### Critical Risks
- **Split-brain scenarios** if regions become isolated
- **Data inconsistency** from partial write failures
- **Performance degradation** from network latency
- **Cascade failures** if one region impacts the other

---

## Approach 2: Background Sync Job

### Implementation Requirements

#### Sync Architecture
The background sync process leverages Cloud Run Jobs and Cloud Scheduler to provide automated, scheduled synchronization between regions.

```typescript
class IdentityBackupService {
  async syncToWestRegion() {
    const changes = await this.getChangesSinceLastSync();
    const gcipBackup = await this.exportGCIPState();
    
    await Promise.all([
      this.syncDatabaseChanges(changes),
      this.syncSpiceDBRelationships(changes),
      this.storeGCIPBackup(gcipBackup)
    ]);
  }
}
```

#### Infrastructure Requirements
- **Cloud Run Job** for sync execution
- **Cloud Scheduler** for periodic triggering
- **Secrets** for cross-region database access
- **Monitoring** for sync health and lag detection

It would be relatively simple to create a purpose built container from the identity server scripts to run the sync job. The same pattern for dependency injection and one time runs can be borrowed from the local seed emulator scripts.

#### GCIP State Management

##### Automated Tenant Recreation Scripts
```typescript
// apps/identity-server/src/sync/gcip-backup.service.ts
@Injectable()
export class GCIPBackupService {
  async exportTenantConfigurations(): Promise<TenantBackup[]> {
    const tenants = await this.admin.auth().listTenants();
    const backups: TenantBackup[] = [];
    
    for (const tenant of tenants.tenants) {
      const config = await this.admin.auth(tenant.tenantId).tenantManager().getTenant(tenant.tenantId);
      const samlConfigs = await this.admin.auth(tenant.tenantId).listProviderConfigs({ type: 'saml' });
      const oidcConfigs = await this.admin.auth(tenant.tenantId).listProviderConfigs({ type: 'oidc' });
      
      backups.push({
        tenantId: tenant.tenantId,
        displayName: tenant.displayName,
        config: {
          allowPasswordSignup: config.allowPasswordSignup,
          enableEmailLinkSignin: config.enableEmailLinkSignin,
          // ... other config properties
        },
        samlConfigs: samlConfigs.providerConfigs,
        oidcConfigs: oidcConfigs.providerConfigs,
        timestamp: new Date().toISOString()
      });
    }
    
    return backups;
  }

  async restoreTenant(backup: TenantBackup, targetRegion: string): Promise<void> {
    const auth = getAuth(getApp(`disaster-recovery-${targetRegion}`));
    
    // Create tenant with same configuration
    const tenant = await auth.tenantManager().createTenant({
      displayName: backup.displayName,
      allowPasswordSignup: backup.config.allowPasswordSignup,
      enableEmailLinkSignin: backup.config.enableEmailLinkSignin,
    });
    
    // Restore SAML configurations
    for (const samlConfig of backup.samlConfigs) {
      await auth.createProviderConfig({
        providerId: samlConfig.providerId,
        type: 'saml',
        samlConfig: samlConfig.samlConfig
      });
    }
    
    // Restore OIDC configurations  
    for (const oidcConfig of backup.oidcConfigs) {
      await auth.createProviderConfig({
        providerId: oidcConfig.providerId,
        type: 'oidc',
        oidcConfig: oidcConfig.oidcConfig
      });
    }
  }
}
```

##### User Migration Procedures
```typescript
// apps/identity-server/src/sync/user-migration.service.ts
@Injectable()
export class UserMigrationService {
  async migrateUsers(tenantBackup: TenantBackup): Promise<MigrationReport> {
    const sourceAuth = getAuth(getApp('primary'));
    const targetAuth = getAuth(getApp('disaster-recovery'));
    
    const migrationReport: MigrationReport = {
      totalUsers: 0,
      migratedUsers: 0,
      failedUsers: 0,
      errors: []
    };
    
    // Export users from primary GCIP
    const listUsersResult = await sourceAuth.listUsers();
    migrationReport.totalUsers = listUsersResult.users.length;
    
    for (const user of listUsersResult.users) {
      try {
        // Create user in target region
        const userRecord = await targetAuth.createUser({
          uid: user.uid,
          email: user.email,
          emailVerified: user.emailVerified,
          displayName: user.displayName,
          phoneNumber: user.phoneNumber,
          photoURL: user.photoURL,
          disabled: user.disabled,
          metadata: {
            creationTime: user.metadata.creationTime,
            lastSignInTime: user.metadata.lastSignInTime
          },
          customClaims: user.customClaims,
          providerData: user.providerData
        });
        
        migrationReport.migratedUsers++;
        
      } catch (error) {
        migrationReport.failedUsers++;
        migrationReport.errors.push({
          uid: user.uid,
          email: user.email,
          error: error.message
        });
      }
    }
    
    return migrationReport;
  }
}
```

##### Change Data Capture System
```typescript
// apps/identity-server/src/sync/change-detector.service.ts
@Injectable()
export class ChangeDetectorService {
  async getChangesSinceLastSync(lastSyncTimestamp: Date): Promise<IdentityChanges> {
    const identityChanges = await this.identityUserRepo
      .createQueryBuilder('user')
      .where('user.updatedAt > :timestamp', { timestamp: lastSyncTimestamp })
      .orWhere('user.createdAt > :timestamp', { timestamp: lastSyncTimestamp })
      .getMany();
    
    const coreUserChanges = await this.coreUserRepo
      .createQueryBuilder('user')
      .where('user.updatedAt > :timestamp', { timestamp: lastSyncTimestamp })
      .orWhere('user.createdAt > :timestamp', { timestamp: lastSyncTimestamp })
      .getMany();
    
    const spiceDBChanges = await this.getSpiceDBRelationshipChanges(lastSyncTimestamp);
    
    return {
      identityUsers: identityChanges,
      coreUsers: coreUserChanges,
      relationships: spiceDBChanges,
      timestamp: new Date()
    };
  }
  
  private async getSpiceDBRelationshipChanges(since: Date): Promise<RelationshipChange[]> {
    // Query SpiceDB changelog or implement change tracking
    // This would require custom change tracking in SpiceDB operations
    return [];
  }
}
```

##### Monitoring and Alerting Setup
```typescript
// apps/identity-server/src/sync/monitoring.service.ts
@Injectable()
export class SyncMonitoringService {
  async recordSyncMetrics(syncResult: SyncResult): Promise<void> {
    // Send metrics to Cloud Monitoring
    const metricClient = new monitoring.MetricServiceClient();
    
    const timeSeriesData = [{
      metric: {
        type: 'custom.googleapis.com/identity/sync_duration',
        labels: {
          'sync_type': 'disaster_recovery',
          'target_region': syncResult.targetRegion,
          'status': syncResult.success ? 'success' : 'failure'
        }
      },
      points: [{
        interval: {
          endTime: { seconds: Math.floor(Date.now() / 1000) }
        },
        value: {
          doubleValue: syncResult.durationMs
        }
      }]
    }];
    
    await metricClient.createTimeSeries({
      name: `projects/core-platform-prod-beam`,
      timeSeries: timeSeriesData
    });
    
    // Send alert if sync fails
    if (!syncResult.success) {
      await this.sendSyncFailureAlert(syncResult);
    }
  }
  
  private async sendSyncFailureAlert(syncResult: SyncResult): Promise<void> {
    // Integration with existing notification system
    await this.notification.send({
      type: NotificationType.SYSTEM_ALERT,
      recipient: '<EMAIL>',
      subject: 'Identity Disaster Recovery Sync Failed',
      body: `
        Sync to ${syncResult.targetRegion} failed at ${syncResult.timestamp}
        
        Error: ${syncResult.error}
        Records processed: ${syncResult.recordsProcessed}
        Duration: ${syncResult.durationMs}ms
        
        Please check Cloud Run logs for detailed information.
      `
    });
  }
}
```

### Pros
✅ **Lower complexity** in core application logic  
✅ **Minimal performance impact** on production workloads  
✅ **Easier to implement** (1-3 months development)  
✅ **Lower operational overhead** for normal operations  
✅ **Gradual rollout possible** without affecting existing flows

### Cons
❌ **Data loss window** (5-15 minutes of recent changes)  
❌ **Complex recovery procedures** requiring GCIP recreation  
❌ **Sync lag during high-traffic periods**  
❌ **Manual intervention** likely needed during failover  
❌ **Testing complexity** for full disaster scenarios

### Critical Risks
- **Sync job failures** leading to stale backup data
- **GCIP recreation complexity** during actual disaster
- **User session invalidation** during failover
- **Extended downtime** during manual recovery process

---

## Alternative Approaches

### Option 3: Database-Native Replication + GCIP Documentation
**Strategy**: Implement PostgreSQL cross-region replication with comprehensive GCIP runbooks

**Pros**: Leverages database-level consistency, minimal application changes  
**Cons**: Still requires manual GCIP recreation, complex failover procedures

### Option 4: Event-Driven Sync via Pub/Sub
**Strategy**: Use existing Pub/Sub infrastructure to stream identity changes

**Pros**: Builds on current architecture, real-time capability  
**Cons**: Complex event ordering, potential message loss scenarios

### Option 5: Third-Party Identity Provider Migration
**Strategy**: Migrate away from GCIP to a provider with better multi-region support

**Pros**: Eliminates GCIP single point of failure  
**Cons**: Massive migration effort, business disruption, vendor lock-in risks

---

## Recommendations

### Primary Recommendation: Hybrid Approach
Combine database-level replication with application-layer GCIP backup for optimal balance of complexity and reliability.

#### Phase 1: Foundation (Months 1-2)
1. **Implement PostgreSQL cross-region replication** for identity-db and core-db
2. **Set up SpiceDB replication** between regions
3. **Create GCIP backup automation** with tenant configuration exports

#### Phase 2: Application Integration (Months 2-3)
1. **Build GCIP restoration tooling** for automated user migration
2. **Implement health monitoring** and failover detection
3. **Create disaster recovery runbooks** with step-by-step procedures

#### Phase 3: Testing and Refinement (Month 4)
1. **Conduct disaster recovery drills** with controlled GCIP outages
2. **Optimize sync intervals** based on RTO/RPO requirements
3. **Train operations team** on failover procedures

### Implementation Priority
1. **High Priority**: Database replication (foundation for any approach)
2. **Medium Priority**: GCIP backup automation
3. **Low Priority**: Application-layer dual writes (only if RTO < 5 minutes required)

### Critical Success Factors
- **Comprehensive testing** including full disaster scenarios
- **Clear failover procedures** with defined responsibility matrix
- **Monitoring and alerting** for sync health and lag detection
- **Regular disaster recovery drills** to validate procedures

### Resource Requirements
- **Engineering effort**: 2-3 senior engineers for 3-4 months
- **Infrastructure costs**: ~30% increase for dual-region resources
- **Operational overhead**: Additional monitoring, alerting, and runbook maintenance

---

## Conclusion

The **hybrid approach combining database replication with GCIP backup** provides the best balance of reliability, complexity, and cost. While dual-region writes offer superior consistency, the implementation complexity and failure modes make them unsuitable for the current architecture. Background sync alone is insufficient due to GCIP's external nature requiring manual intervention.

The recommended approach provides:
- **5-15 minute RTO** for database services
- **15-30 minute RTO** for full identity services (including GCIP recreation)
- **Manageable complexity** that aligns with current team capabilities
- **Cost-effective solution** that doesn't require architectural overhaul

**Next Steps**: Begin with Phase 1 implementation focusing on database replication as the foundation for any disaster recovery strategy.