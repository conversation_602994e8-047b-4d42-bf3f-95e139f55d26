{"name": "@bybeam/partner-portal", "version": "0.1.0", "private": true, "type": "module", "scripts": {"start:dev": "next dev -p 3002", "build:prod": "NODE_OPTIONS='--max-old-space-size=4096' next build", "start:prod": "next start", "lint": "next lint", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "chromatic": "chromatic --build-script-name storybook:build", "storybook": "storybook dev -p 6006", "storybook:build": "storybook build"}, "dependencies": {"@apollo/client": "3.11.0", "@apollo/experimental-nextjs-app-support": "^0.11.2", "@bybeam/doctopus-types": "workspace:*", "@bybeam/formatting": "workspace:*", "@bybeam/identity-client": "workspace:*", "@bybeam/platform-lib": "workspace:*", "@bybeam/platform-types": "workspace:*", "@bybeam/verification-types": "workspace:^", "@emotion/react": "11.11.1", "@emotion/styled": "11.11.0", "@grafana/faro-web-sdk": "1.18.1", "@grafana/faro-web-tracing": "1.18.1", "@hookform/error-message": "^2.0.1", "@mui/icons-material": "6.1.1", "@mui/lab": "5.0.0-alpha.71", "@mui/material": "5.15.16", "@mui/styles": "5.15.16", "@opentelemetry/api": "^1.9.0", "@opentelemetry/api-logs": "^0.53.0", "@opentelemetry/instrumentation": "^0.53.0", "@opentelemetry/sdk-logs": "^0.53.0", "@opentelemetry/sdk-trace-node": "^1.26.0", "@preset-sdk/embedded": "^0.1.13", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-form": "^0.1.0", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "1.0.7", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-tooltip": "^1.1.2", "@radix-ui/themes": "^3.2.1", "@react-input/mask": "1.0.20", "@react-pdf/renderer": "3.1.14", "@schematichq/schematic-js": "^0.1.10", "@spotlightjs/spotlight": "2.0.0", "@statelyai/inspect": "^0.4.0", "@tanstack/react-table": "8.10.7", "@vercel/otel": "1.12.0", "@xstate/react": "^4.1.1", "apollo-upload-client": "18.0.1", "classnames": "2.5.1", "core-js": "3.35.1", "csv-parse": "5.5.3", "dayjs": "1.11.10", "firebase": "10.12.2", "flat": "5.0.2", "graphql": "16.9.0", "html-react-parser": "^5.1.8", "imagekitio-react": "4.3.0", "isemail": "3.2.0", "lodash": "4.17.21", "next": "14.2.26", "next-runtime-env": "^3.2.2", "posthog-js": "1.236.1", "posthog-node": "^4.0.1", "react": "18.3.1", "react-dom": "18.3.1", "react-dropzone": "14.3.5", "react-headless-pagination": "^1.1.6", "react-helmet": "6.1.0", "react-hook-form": "^7.54.1", "react-intersection-observer": "^9.13.1", "react-markdown": "8.0.7", "react-modal": "3.16.1", "react-month-picker": "2.2.1", "react-qr-code": "^2.0.15", "react-string-replace": "1.1.1", "regenerator-runtime": "0.14.1", "sanitize-filename": "^1.6.3", "sanitize-html": "^2.12.1", "undici": "^6.21.1", "uuid": "9.0.1", "xstate": "^5.13.2"}, "devDependencies": {"@babel/plugin-transform-runtime": "^7.24.3", "@chromatic-com/storybook": "^3.2.2", "@esbuild-plugins/node-globals-polyfill": "0.2.3", "@esbuild-plugins/node-modules-polyfill": "0.2.2", "@grafana/faro-webpack-plugin": "^0.5.1", "@storybook/addon-essentials": "^8.4.1", "@storybook/addon-links": "^8.4.1", "@storybook/addon-onboarding": "^8.4.1", "@storybook/blocks": "^8.4.1", "@storybook/experimental-addon-test": "^8.4.1", "@storybook/experimental-nextjs-vite": "^8.4.1", "@storybook/nextjs": "^8.4.1", "@storybook/react": "^8.4.1", "@storybook/test": "^8.4.1", "@testing-library/jest-dom": "6.4.8", "@testing-library/react": "16.0.0", "@types/apollo-upload-client": "^18.0.0", "@types/flat": "5.0.5", "@types/lodash": "^4.17.10", "@types/node": "22.0.0", "@types/react": "18.3.3", "@types/react-dom": "18.3.1", "@types/react-table": "7.7.19", "@types/sanitize-html": "^2.11.0", "@types/uuid": "9.0.8", "@vitejs/plugin-react": "4.3.1", "@vitest/browser": "2.1.9", "@vitest/coverage-v8": "2.1.9", "autoprefixer": "10.4.16", "buffer": "^6.0.3", "chromatic": "11.18.1", "graphql-tag": "2.12.6", "jsdom": "^25.0.1", "mockdate": "3.0.5", "next-router-mock": "^0.9.13", "playwright": "^1.48.2", "postcss": "8.4.49", "postcss-flexbugs-fixes": "5.0.2", "postcss-import": "15.1.0", "postcss-nesting": "^13.0.0", "postcss-preset-env": "10.0.0", "storybook": "^8.4.1", "storybook-addon-apollo-client": "7.3.0", "tailwindcss": "3.4.0", "typescript": "5.5.4", "vite-plugin-graphql-loader": "^3.0.1", "vite-tsconfig-paths": "5.0.1", "vitest": "2.1.9"}}