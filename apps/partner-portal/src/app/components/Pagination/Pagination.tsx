import cx from 'classnames';
import { Pagination as ReactHeadlessPagination } from 'react-headless-pagination';
import { Button } from '@radix-ui/themes';
import { ArrowLeftIcon, ArrowRightIcon } from '@radix-ui/react-icons';

const Pagination = ({
  edgePageCount = 2,
  middlePagesSiblingCount = 1,
  page,
  setPage,
  totalPages = 10,
  truncableClassName = 'w-10 px-0.5',
  truncableText = '...',
}: {
  edgePageCount?: number;
  middlePagesSiblingCount?: number;
  page: number;
  setPage: (page: number) => void;
  totalPages?: number;
  truncableClassName?: string;
  truncableText?: string;
}) => {
  const handlePageChange = (page: number) => {
    setPage(page);
  };

  return (
    <ReactHeadlessPagination
      totalPages={totalPages}
      edgePageCount={edgePageCount}
      middlePagesSiblingCount={middlePagesSiblingCount}
      truncableClassName={truncableClassName}
      truncableText={truncableText}
      currentPage={page}
      setCurrentPage={handlePageChange}
      className="flex items-center w-full h-10 text-sm select-none"
    >
      <ReactHeadlessPagination.PrevButton
        as={<Button variant="ghost" />}
        className={cx(
          'flex items-center mr-2 text-gray-500 hover:text-gray-600 dark:hover:text-gray-200',
          {
            'cursor-pointer': page !== 0,
            'opacity-50': page === 0,
          },
        )}
      >
        <ArrowLeftIcon className="mr-3" />
        Previous
      </ReactHeadlessPagination.PrevButton>

      <nav className="flex justify-center flex-grow">
        <ul className="flex items-center">
          <ReactHeadlessPagination.PageButton
            activeClassName="bg-[var(--blue-4)] text-primary-600"
            inactiveClassName="text-gray-500"
            className={
              'flex items-center justify-center hover:text-primary-600 focus:font-bold focus:text-primary-600 focus:outline-none h-10 w-10 rounded-full cursor-pointer'
            }
          />
        </ul>
      </nav>

      <ReactHeadlessPagination.NextButton
        as={<Button variant="ghost" />}
        className={cx(
          'flex items-center mr-2 text-gray-500 hover:text-gray-600 dark:hover:text-gray-200',
          {
            'cursor-pointer': page !== totalPages - 1,
            'opacity-50': page === totalPages - 1,
          },
        )}
      >
        Next
        <ArrowRightIcon className="ml-3" />
      </ReactHeadlessPagination.NextButton>
    </ReactHeadlessPagination>
  );
};

export default Pagination;
