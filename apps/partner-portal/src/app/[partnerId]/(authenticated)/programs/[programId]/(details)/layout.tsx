'use client';
import CanAccess from '@/app/components/features/CanAccess';
import Page from '@/app/components/ui/Page/Page';
import PageNavigation from '@/app/components/ui/Page/Parts/PageNavigation';
import SidesheetToggle from '@/app/components/ui/Page/Parts/SidesheetToggle';
import { POSTHOG_FEATURE_FLAGS } from '@/app/posthog';
import { useSidesheet } from '@/app/providers/SidesheetProvider';
import { ChipSize } from '@/spa-legacy/common/components/Chip';
import useProgram from '@/spa-legacy/common/hooks/contexts/useProgram';
import { PORTAL_ROUTES } from '@/spa-legacy/portal/PortalRoutes';
import ClosedChip from '@/spa-legacy/portal/components/ClosedChip';
import { makeRoute } from '@/spa-legacy/utilities/Routes';
import { checkFeature } from '@bybeam/platform-lib/features/check';
import { FeatureName, ProgramStatus } from '@bybeam/platform-types';
import { PlusIcon } from '@radix-ui/react-icons';
import { Button, Flex } from '@radix-ui/themes';
import Link from 'next/link';
import { useParams, usePathname } from 'next/navigation';
import { usePostHog } from 'posthog-js/react';
import { useMemo } from 'react';
import ProgramStats from './components/ProgramStats';
import styles from './layout.module.css';
import BulkPayments from './components/BulkPayments/BulkPayments';

export default function ProgramRouteLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const posthog = usePostHog();
  const program = useProgram();
  const { isSidesheetOpen } = useSidesheet();

  const { partnerId, programId } = useParams();
  const pathname = usePathname();

  const pathRoot = useMemo(() => {
    return `/${partnerId}/programs/${programId}`;
  }, [partnerId, programId]);

  const childPages = {
    root: pathRoot,
    programConfig: `${pathRoot}/programConfig`,
  };

  return (
    <Page>
      <Page.Header>
        <div className={styles.programNavigation}>
          <Page.Breadcrumbs>
            <Page.Breadcrumbs.Crumb>
              {program?.name}
              {program?.status === ProgramStatus.Closed && <ClosedChip size={ChipSize.M} />}
            </Page.Breadcrumbs.Crumb>
          </Page.Breadcrumbs>
          <PageNavigation>
            <PageNavigation.Link active={pathname === childPages.root} asChild>
              <Link href={{ pathname: childPages.root }}>Cases</Link>
            </PageNavigation.Link>
            <PageNavigation.Link active={pathname.includes(childPages.programConfig)} asChild>
              <Link href={{ pathname: childPages.programConfig }}>Program Details</Link>
            </PageNavigation.Link>
          </PageNavigation>
        </div>
        <SidesheetToggle />
      </Page.Header>
      <Page.Content className={styles.pageContainer}>
        {children}
        {isSidesheetOpen ? (
          <aside className={styles.sidesheet}>
            <ProgramStats />
            {posthog.isFeatureEnabled(POSTHOG_FEATURE_FLAGS.bulkPaymentWizard) && <BulkPayments />}
            <Flex p="4">
              {program && posthog.isFeatureEnabled(POSTHOG_FEATURE_FLAGS.caseDetails2024)
                ? checkFeature(program?.features, FeatureName.WorkflowCreateApplications) && (
                    <CanAccess resource={{ objectId: program.id, objectType: 'PROGRAM' }}>
                      <Button type="button" variant="outline" asChild>
                        <Link
                          href={{
                            pathname: makeRoute(PORTAL_ROUTES.CREATE_ACCOUNT, {
                              programId: program.id,
                            }),
                          }}
                        >
                          <PlusIcon /> Add New Applicant
                        </Link>
                      </Button>
                    </CanAccess>
                  )
                : null}
            </Flex>
          </aside>
        ) : null}
      </Page.Content>
    </Page>
  );
}
