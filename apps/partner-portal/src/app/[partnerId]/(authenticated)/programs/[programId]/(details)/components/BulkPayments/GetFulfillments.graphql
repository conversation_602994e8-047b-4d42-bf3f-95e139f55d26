query GetFulfillments($filter: FulfillmentFilter!, $pagination: OffsetPagination) {
  fulfillments(filter: $filter, pagination: $pagination) {
    pageInfo {
      count
      nextCursor
    }
    fulfillments {
      id
      fund {
        id
        name
      }
      approvedAmount
      payments {
        id
        status
        method
        payeeType
        payee {
          ... on Vendor {
            id
            name
            payeeType
          }
          ... on User {
            id
            name
            payeeType
            applicantProfile {
              id
              applicantType {
                id
              }
            }
          }
        }
      }
    }
  }
}
