import { PostHog } from 'posthog-node';

export default function PostHogClient() {
  if (!process.env.NEXT_PUBLIC_POSTHOG_KEY) {
    return null;
  }
  const posthogClient = new PostHog(process.env.NEXT_PUBLIC_POSTHOG_KEY, {
    host: process.env.NEXT_PUBLIC_POSTHOG_HOST,
    flushAt: 1,
    flushInterval: 0,
  });
  return posthogClient;
}

/**
 * Posthog feature flags are defined and configured in the Posthog Dashboard.
 * Each relevant project in posthog must define the feature flag.
 */
export const POSTHOG_FEATURE_FLAGS = {
  accessRequests: 'access-requests',
  bannerMessagesPage: 'banner-messages-page',
  bulkProgramReferral: 'bulk-program-referral',
  bulkPaymentWizard: 'bulk-payment-wizard',
  participantsTab: 'participants-tab',
  casePaymentsPage: 'case-payments-page',
  caseDetailsDocumentsList: 'case-details-documents-list',
  caseDetails2024: 'case-details-2024',
  changelog: 'changelog',
  docsAi2024Q4: 'docs-ai-2024-Q4',
  documentsExtractedFields: 'documents-extracted-fields',
  fundsPage: 'funds-page',
  integrations: 'integrations',
  presetEmbeddedAnalytics: 'preset-embedded-analytics',
  presetDisableStandardReport: 'preset-disable-standard-report',
  programCreation: 'program-creation',
  programWorkflowPage: 'program-workflow-page',
  programNotificationsPage: 'program-notifications-page',
  paymentMailingAddress: 'payment-mailing-address',
  savedViews: 'saved-views',
  searchElasticsearch: 'search-elasticsearch',
  sso: 'sso',
  verificationConfig: 'verification-config',
  verificationStatus2025Q2: 'verification-status-2025-Q2',
};
