import { FulfillmentFilter, OffsetPagination } from '@bybeam/platform-types';
import { AdminContext } from '@platform-api/@types/graphql.js';
import { GraphQLFieldResolver } from 'graphql';

const fulfillments: GraphQLFieldResolver<
  void,
  AdminContext,
  { filter?: FulfillmentFilter; pagination?: OffsetPagination }
> = async (_, { filter, pagination }, context) => {
  const { nodes, pageInfo } = await context.services.fulfillments.findAndCount(
    context.token,
    filter,
    pagination,
  );
  return { fulfillments: nodes, pageInfo };
};

export default fulfillments;
