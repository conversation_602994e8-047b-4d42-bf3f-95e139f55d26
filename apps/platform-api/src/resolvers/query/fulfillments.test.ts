import { describe, expect, it, vi } from 'vitest';
import { GraphQLResolveInfo } from 'graphql';
import { AdminContext } from '@platform-api/@types/graphql.js';
import { FulfillmentFilter, OffsetPagination } from '@bybeam/platform-types';
import { mockAdminToken } from '@platform-api-test/mocks.js';
import fulfillments from './fulfillments.js';

describe('fulfillments query resolver', () => {
  it('should call fulfillments service with correct parameters', async () => {
    const findAndCountFn = vi.fn().mockResolvedValueOnce({
      nodes: [
        { id: 'fulfillment-1', approvedAmount: 1000 },
        { id: 'fulfillment-2', approvedAmount: 2000 },
      ],
      pageInfo: { count: 2 },
    });

    const filter: FulfillmentFilter = {
      ids: ['fulfillment-1', 'fulfillment-2'],
    };
    const pagination: OffsetPagination = { page: 0, take: 10 };

    const mockContext = {
      token: mockAdminToken,
      services: {
        fulfillments: {
          findAndCount: findAndCountFn,
        },
      },
    } as unknown as AdminContext;

    const result = await fulfillments(
      undefined,
      { filter, pagination },
      mockContext,
      {} as GraphQLResolveInfo,
    );

    expect(findAndCountFn).toHaveBeenCalledWith(mockAdminToken, filter, pagination);
    expect(result).toEqual({
      fulfillments: [
        { id: 'fulfillment-1', approvedAmount: 1000 },
        { id: 'fulfillment-2', approvedAmount: 2000 },
      ],
      pageInfo: { count: 2 },
    });
  });

  it('should handle empty filter and pagination', async () => {
    const findAndCountFn = vi.fn().mockResolvedValueOnce({
      nodes: [],
      pageInfo: { count: 0 },
    });

    const mockContext = {
      token: mockAdminToken,
      services: {
        fulfillments: {
          findAndCount: findAndCountFn,
        },
      },
    } as unknown as AdminContext;

    const result = await fulfillments(undefined, {}, mockContext, {} as GraphQLResolveInfo);

    expect(findAndCountFn).toHaveBeenCalledWith(mockAdminToken, undefined, undefined);
    expect(result).toEqual({
      fulfillments: [],
      pageInfo: { count: 0 },
    });
  });

  it('should pass through service errors', async () => {
    const serviceError = new Error('Database connection failed');
    const findAndCountFn = vi.fn().mockRejectedValueOnce(serviceError);

    const mockContext = {
      token: mockAdminToken,
      services: {
        fulfillments: {
          findAndCount: findAndCountFn,
        },
      },
    } as unknown as AdminContext;

    await expect(
      fulfillments(
        undefined,
        { filter: { ids: ['test-id'] } },
        mockContext,
        {} as GraphQLResolveInfo,
      ),
    ).rejects.toThrow('Database connection failed');
  });
});
