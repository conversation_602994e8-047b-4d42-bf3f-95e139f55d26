import IsAdmin from '../composition/IsAdmin.js';
import IsAuthenticated from '../composition/IsAuthenticated.js';
import accessRequests from './accessRequests.js';
import aggregateApplication from './aggregateApplication.js';
import aggregatePayment from './aggregatePayment.js';
import applications from './applications.js';
import cases from './cases.js';
import changelogs from './changelogs.js';
import currentUser from './currentUser.js';
import fulfillments from './fulfillments.js';
import hasAccess from './hasAccess.js';
import incomeLimitAreas from './incomeLimitAreas.js';
import partners from './partners.js';
import payments from './payments.js';
import users from './users.js';

export const resolvers = {
  accessRequests,
  aggregateApplication,
  aggregatePayment,
  applications,
  cases,
  changelogs,
  currentUser,
  fulfillments,
  hasAccess,
  incomeLimitAreas,
  partners,
  payments,
  users,
};

export const composition = {
  accessRequests: [IsAdmin()],
  aggregateApplication: [IsAdmin()],
  aggregatePayment: [IsAdmin()],
  applications: [IsAuthenticated()],
  cases: [IsAuthenticated()],
  changelogs: [],
  currentUser: [],
  fulfillments: [IsAuthenticated()],
  hasAccess: [IsAdmin()],
  incomeLimitAreas: [],
  partners: [],
  payments: [IsAdmin()],
  users: [IsAdmin()],
};
