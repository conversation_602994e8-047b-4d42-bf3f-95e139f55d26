import { FulfillmentFilter, OffsetPagination } from '@bybeam/platform-types';
import { FulfillmentRepository as IFulfillmentRepository } from '@platform-api/@types/repositories/index.js';
import { In } from 'typeorm';

export async function findAndCountIds(
  this: IFulfillmentRepository,
  { filter, pagination }: { filter: FulfillmentFilter; pagination?: OffsetPagination },
): Promise<{ ids: string[]; count: number }> {
  const query = this.createQueryBuilder('fulfillment').select('fulfillment.id', 'id');

  if (filter?.ids?.length) {
    query.andWhere({ id: In(filter.ids) });
  }

  if (pagination) {
    query.skip(pagination.page * pagination.take);
    query.take(pagination.take);
  }

  // Get paginated IDs
  const entities = await query.getRawMany();

  // Get total count (without pagination)
  const countQuery = this.createQueryBuilder('fulfillment').select(
    'COUNT(fulfillment.id)',
    'count',
  );

  // Apply the same filter conditions as the main query
  if (filter?.ids?.length) {
    countQuery.andWhere({ id: In(filter.ids) });
  }

  const { count } = await countQuery.getRawOne();
  console.log({ entities, count });

  return { ids: entities.map(({ id }) => id), count: count || 0 };
}
