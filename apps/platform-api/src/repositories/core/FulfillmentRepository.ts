import { FulfillmentFilter, OffsetPagination } from '@bybeam/platform-types';
import { FulfillmentRepository as IFulfillmentRepository } from '@platform-api/@types/repositories/index.js';
import { In } from 'typeorm';

export async function findAndCountIds(
  this: IFulfillmentRepository,
  { filter, pagination }: { filter: FulfillmentFilter; pagination?: OffsetPagination },
): Promise<{ ids: string[]; count: number }> {
  const query = this.createQueryBuilder('fulfillment').select('fulfillment.id', 'id');

  if (filter?.ids?.length) {
    query.andWhere({ id: In(filter.ids) });
  }

  if (pagination) {
    query.skip(pagination.page * pagination.take);
    query.take(pagination.take);
  }

  const entities = await query.getRawMany();
  const rawOne = await this.createQueryBuilder('fulfillment')
    .select('fulfillment.id', 'id')
    .addSelect('COUNT(DISTINCT fulfillment.id)', 'count')
    .groupBy('fulfillment.id')
    .andWhere({ id: In(filter?.ids ?? []) })
    .getRawOne();
  console.log('dbz', { entities, rawOne });
  return { ids: entities.map(({ id }) => id), count: rawOne?.count ?? entities.length };
}
