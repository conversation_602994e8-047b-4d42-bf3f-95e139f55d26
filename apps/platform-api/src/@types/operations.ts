import {
  AddApplicationVersionInput,
  AddBulkCommentInput,
  AddCommentInput,
  AddProgramFundInput,
  Application,
  ApplicationContentInput,
  ApproveCasePaymentsInput,
  BulkMutationResponse,
  BulkIssueFundsInput,
  Case,
  ClaimFundsInput,
  CreateApplicationInput,
  CreateProgramInput,
  CreateBulkProgramReferralInput,
  CreateUserInput,
  CreateVendorInput,
  CreateW9TaxFormInput,
  EmailWebhookBody,
  ExpediteCasesInput,
  Fulfillment,
  HandleSmsInboundsInput,
  IssueFundsInput,
  LinkToLegacyUserInput,
  MutationResponse,
  OverrideCasePaymentsInput,
  Payee,
  Program,
  ResetPaymentInput,
  SendSMSHelpInput,
  TransitionCaseStatusesInput,
  UnsubscribeCommunicationChannelInput,
  UpdateApplicationInput,
  UpdatePayeeInput,
  UpdateUserInput,
  UpdateUserProfileInput,
  UpdateVendorInput,
  UploadEntityDocumentInput,
  User,
  Vendor,
  WebhookBody,
  RemoveProgramFundInput,
  Address,
  Nullable,
} from '@bybeam/platform-types';
import { UpsertLookupConfigInput } from '@bybeam/verification-types';
import HandleParticipantLinkOperation from '@platform-api/operations/cases/CaseParticipantLinkOperation.js';
import { Response } from 'express';
import { AdminToken, LoginToken } from './authentication.js';

export interface Operation<Input, Output, T = LoginToken> {
  run(token: T, input: Input, res?: Response): Promise<Output>;
}

// --- Applicants ---
export type PopulateApplicantProfileAnswersOperation = Operation<Application[], void, LoginToken>;

// --- Applications ---
export type AddApplicationVersionOperation = Operation<
  AddApplicationVersionInput,
  MutationResponse<Application>,
  AdminToken
>;

export type CreateApplicationOperation = Operation<
  CreateApplicationInput,
  MutationResponse<Application>,
  LoginToken
>;

export type SubmitApplicationOperation = Operation<
  Pick<Application, 'id'>,
  MutationResponse<Application>,
  LoginToken
>;

export type UpdateApplicationContentOperation = Operation<
  { versionId: string; content: ApplicationContentInput },
  MutationResponse<Application>,
  LoginToken
>;

export type UpdateApplicationOperation = Operation<
  UpdateApplicationInput,
  MutationResponse<Application>,
  LoginToken
>;

// ------ Cases ------
export type ApproveCasePaymentsOperation = Operation<
  ApproveCasePaymentsInput,
  BulkMutationResponse<Case>,
  AdminToken
>;

export type OverrideCasePaymentsOperation = Operation<
  OverrideCasePaymentsInput,
  BulkMutationResponse<Case>,
  AdminToken
>;

export type ExpediteCasesOperation = Operation<
  ExpediteCasesInput,
  BulkMutationResponse<Case>,
  AdminToken
>;

export type UndoExpediteCasesOperation = Operation<
  ExpediteCasesInput,
  BulkMutationResponse<Case>,
  AdminToken
>;

export type TransitionCaseStatusOperation = Operation<
  TransitionCaseStatusesInput,
  BulkMutationResponse<Case>,
  AdminToken
>;

export type AddCommentOperation = Operation<AddCommentInput, MutationResponse<Case>, AdminToken>;
export type AddBulkCommentOperation = Operation<
  AddBulkCommentInput,
  BulkMutationResponse<Case>,
  AdminToken
>;
export type AddParticipantCommentOperation = Operation<
  AddCommentInput,
  MutationResponse<Case>,
  LoginToken
>;

export type HandleEmailCommentCallbackOperation = Operation<EmailWebhookBody, void, undefined>;

// ----- Payments------
export type ClaimFundsOperation = Operation<
  ClaimFundsInput,
  MutationResponse<Fulfillment>,
  LoginToken
>;

export type HandlePaymentCallbackOperation = Operation<WebhookBody, void, undefined>;

export type IssueFundsOperation = Operation<
  IssueFundsInput,
  MutationResponse<Fulfillment>,
  AdminToken
>;

export type BulkIssueFundsOperation = Operation<
  BulkIssueFundsInput,
  BulkMutationResponse<Fulfillment>,
  AdminToken
>;

export type ResetPaymentOperation = Operation<ResetPaymentInput, void, undefined>;

export type UpsertBankAccountOperation = Operation<
  UpdatePayeeInput,
  MutationResponse<Payee>,
  LoginToken
>;

export type FindAndSavePaymentMailingAddressOperation = Operation<
  { paymentId: string; autoSave?: boolean },
  Nullable<Address>,
  Nullable<LoginToken>
>;

// ----- Programs ------
export type UploadVerificationFileOperation = Operation<
  UploadEntityDocumentInput,
  MutationResponse<Program>,
  AdminToken
>;

export type UpdateLookupConfigOperation = Operation<
  UpsertLookupConfigInput,
  MutationResponse<Program>,
  AdminToken
>;

export type CreateProgramOperation = Operation<
  CreateProgramInput,
  MutationResponse<Program>,
  AdminToken
>;

export type RemoveProgramFundOperation = Operation<
  RemoveProgramFundInput,
  MutationResponse<Program>,
  AdminToken
>;

export type AddProgramFundOperation = Operation<
  AddProgramFundInput,
  MutationResponse<Program>,
  AdminToken
>;

// ------- Users -------
export type CreateApplicantOperation = Operation<
  CreateUserInput,
  MutationResponse<User>,
  AdminToken
>;

export type EditUserDetailsOperation = Operation<
  UpdateUserInput,
  MutationResponse<User>,
  AdminToken
>;

export type UpdateUserProfileOperation = Operation<
  UpdateUserProfileInput,
  MutationResponse<User>,
  LoginToken
>;

export type LinkLegacyUserOperationInput = LinkToLegacyUserInput & {
  includeDeactivatedLegacyUser?: boolean;
};

export type LinkLegacyUserOperation = Operation<
  LinkLegacyUserOperationInput,
  MutationResponse<User>,
  AdminToken | undefined
>;

export type CreateW9TaxFormOperation = Operation<
  CreateW9TaxFormInput,
  MutationResponse<User, Record<string, never>>,
  LoginToken
>;

export type UnsubscribeCommunicationChannelOperation = Operation<
  UnsubscribeCommunicationChannelInput,
  void,
  undefined
>;

export type SendSMSHelpOperation = Operation<SendSMSHelpInput, void, undefined>;

export type CreateBulkProgramReferralOperation = Operation<
  CreateBulkProgramReferralInput,
  BulkMutationResponse<User>,
  AdminToken
>;

// ------ Vendors ------
export type CreateVendorOperation = Operation<
  CreateVendorInput,
  MutationResponse<Vendor>,
  AdminToken
>;
export type EditVendorOperation = Operation<
  UpdateVendorInput,
  MutationResponse<Vendor>,
  AdminToken
>;

// ------- Communications -------
export type HandleSmsInboundsOperation = Operation<HandleSmsInboundsInput, void, undefined>;

// Operations are used for complex tasks where many different
// services may be involved. They may depend on any number of services
// as well as any other operations. They are intended to be single-
// responsibility classes.
export interface Operations {
  applicants: {
    populateProfileAnswers: PopulateApplicantProfileAnswersOperation;
  };
  applications: {
    addVersion: AddApplicationVersionOperation;
    create: CreateApplicationOperation;
    submit: SubmitApplicationOperation;
    updateContent: UpdateApplicationContentOperation;
    update: UpdateApplicationOperation;
  };
  cases: {
    approvePayments: ApproveCasePaymentsOperation;
    expedite: ExpediteCasesOperation;
    handleLink: HandleParticipantLinkOperation;
    overridePayments: OverrideCasePaymentsOperation;
    transitionStatus: TransitionCaseStatusOperation;
    undoExpedite: UndoExpediteCasesOperation;
    addComment: AddCommentOperation;
    addBulkComment: AddBulkCommentOperation;
    addParticipantComment: AddParticipantCommentOperation;
    handleEmailCommentCallback: HandleEmailCommentCallbackOperation;
  };
  payments: {
    claimFunds: ClaimFundsOperation;
    handleCallback: HandlePaymentCallbackOperation;
    issueFunds: IssueFundsOperation;
    bulkIssueFunds: BulkIssueFundsOperation;
    reset: ResetPaymentOperation;
    upsertBankAccount: UpsertBankAccountOperation;
    findAndSaveMailingAddress: FindAndSavePaymentMailingAddressOperation;
  };
  programs: {
    create: CreateProgramOperation;
    uploadVerificationFile: UploadVerificationFileOperation;
    upsertLookupConfig: UpdateLookupConfigOperation;
    addProgramFund: AddProgramFundOperation;
    removeProgramFund: RemoveProgramFundOperation;
  };
  users: {
    createApplicant: CreateApplicantOperation;
    createW9TaxForm: CreateW9TaxFormOperation;
    createBulkProgramReferral: CreateBulkProgramReferralOperation;
    editDetails: EditUserDetailsOperation;
    linkLegacyUser: LinkLegacyUserOperation;
    sendSMSHelp: SendSMSHelpOperation;
    unsubscribeCommunicationChannel: UnsubscribeCommunicationChannelOperation;
    updateProfile: UpdateUserProfileOperation;
  };
  vendors: { create: CreateVendorOperation; update: EditVendorOperation };
  communications: {
    handleSmsInbounds: HandleSmsInboundsOperation;
  };
}
