import { ActiveLabel, DocumentField, DocumentTag, Feedback, Summary } from '@bybeam/doctopus-types';
import {
  AccessRequestList,
  CanAccessResponse,
  CheckPermissionRequest,
  CreateRelationshipsRequest,
  CreateUserRequest,
  CreateUserResponse,
  GetAccessRequests,
  GetUserRequest,
  IdentityUser,
  ReadPortalRolesRequest,
  ReviewAccessRequest,
  Role,
  SendMagicLinkRequest,
  SendVerificationEmailRequest,
  UpdateUserRequest,
  UserResponse,
  VerifyEmailRequest,
} from '@bybeam/identity-client/types';
import {
  AddNote,
  Address,
  AddressRelation,
  Admin,
  AdminFilter,
  AdminSortColumn,
  AggregateApplicantsReferred,
  AggregateApplication,
  AggregateApplicationFilter,
  AggregatePaymentFilter,
  AggregatePayments,
  AnalyticsResource,
  ApplicantProfile,
  ApplicantProfileConfiguration,
  ApplicantType,
  Application,
  ApplicationAnswer,
  ApplicationAnswers,
  ApplicationFilter,
  ApplicationScore,
  ApplicationVerification,
  ApplicationVersion,
  AssignToCaseInput,
  Assignment,
  AutoAssignInput,
  BankAccount,
  BulkOperation,
  BulkOperationStatus,
  BulkPublishInput,
  BulkPublishResult,
  Case,
  CaseFilter,
  CaseMetadata,
  CaseParticipant,
  CaseSortColumn,
  CaseTag,
  Changelog,
  CheckFeature,
  Comment,
  CommunicationChannelsConfiguration,
  CreateAccessRequestInput,
  CreateAddressInput,
  CreateAdminInput,
  CreateAnswerInput,
  CreateCaseInput,
  CreateCaseTagInput,
  CreateIncidentInput,
  CreatePaymentInput,
  CreateProgramApplicationConfigurationInput,
  CreateReferralInput,
  CreateSavedViewInput,
  CreateTagsInput,
  CreateVendorInput,
  CreateWorkflowEventInput,
  CursorPagination,
  DeleteAdminInput,
  DeleteCaseTagInput,
  DeleteTagsInput,
  Document,
  DocumentRelation,
  EligibilityConfig,
  Enrollment,
  EnrollmentIdentifier,
  FeatureName,
  Fulfillment,
  FulfillmentFilter,
  FulfillmentMeta,
  Fund,
  FundConfig,
  FundFilter,
  FundSortColumn,
  FundStats,
  IncidentMessage,
  IncomeLimitArea,
  IncomeLimitAreaFilter,
  Index,
  InvitationCode,
  InviteCaseParticipantInput,
  LinkAttempt,
  LinkToLegacyUserInput,
  MutationResponse,
  Note,
  NoteRelation,
  NoteRelationType,
  Notification,
  NotificationConfig,
  Nullable,
  OffsetPagination,
  Outcome,
  Page,
  Partner,
  PartnerFeature,
  PartnerIncident,
  PartnerWhitelabeling,
  Payee,
  PayeeInput,
  Payment,
  PaymentPattern,
  PaymentPatternInput,
  PaymentSortColumn,
  PinDocumentInput,
  PlatformNotificationType,
  ProfileAnswer,
  Program,
  ProgramApplicantType,
  ProgramApplicationConfiguration,
  ProgramDocument,
  ProgramFeature,
  ProgramFund,
  ProgramReferral,
  ProgramStats,
  PublishInput,
  RemoveEnrollmentOutcomeInput,
  RemoveEnrollmentServiceInput,
  RemoveEntityDocumentsInput,
  RemoveIncidentInput,
  RemoveVendorDocumentsInput,
  SavedView,
  SearchArguments,
  Service,
  Sort,
  SubmitPredictionFeedbackInput,
  Tag,
  TaxForm,
  UnlinkCaseParticipantInput,
  UpdateAdminInput,
  UpdateApplicantProfileInput,
  UpdateNote,
  UpdatePaymentInput,
  UpdateProgramsInput,
  UpdateSavedViewInput,
  UpdateTagInput,
  UpdateVendorInput,
  UploadDocumentsInput,
  UploadEntityDocumentsInput,
  UploadVendorDocumentsInput,
  UpsertEnrollmentInput,
  UpsertEnrollmentOutcomeInput,
  UpsertEnrollmentServiceInput,
  User,
  UserFilter,
  UserSortColumn,
  Vendor,
  VendorFilter,
  VendorSortColumn,
  VendorType,
  VendorTypeRelation,
  Workflow,
  WorkflowEvent,
  WorkflowSummary,
  WorkflowSummaryFilter,
} from '@bybeam/platform-types';
import { VerificationConfiguration } from '@bybeam/verification-client';
import { UpsertLookupConfigInput } from '@bybeam/verification-types';
import { SearchResult } from '@platform-api/repositories/external/Elasticsearch/types.js';
import { Stringifiable } from '@platform-api/repositories/external/PubsubRepository.js';
import { IApplicationAnswerNoteService } from '@platform-api/services/applicationAnswerNotes/ApplicationAnswerNoteService.js';
import { IApplicationAnswerReviewService } from '@platform-api/services/applicationAnswerReviews/ApplicationAnswerReviewService.js';
import { SectionResponse } from '@platform-api/services/applicationVersions/ApplicationVersionService.js';
import { IPresetService } from '@platform-api/services/preset/PresetService.js';
import { Response } from 'express';
import {
  DeleteResult,
  FindManyOptions,
  FindOneOptions,
  FindOptionsWhere,
  UpdateResult,
} from 'typeorm';
import {
  AdminToken,
  AuthenticatedToken,
  BeginSession,
  EndSession,
  LoginToken,
  RecaptchaInput,
} from './authentication.js';
import { ApplicationSubmissionEvent, UploadVerificationFileEvent } from './events.js';
import { SendNotificationRequest } from './externalNotifications.js';

export interface Lockable {
  isLocked(entityId: string): Promise<void>;
  lockEntity(entityId: string): Promise<void>;
  releaseLock(entityId: string): Promise<void>;
}

export interface QueryService<T> {
  exists(filter: FindOptionsWhere<T>): Promise<boolean>;
  findById(id: string): Promise<T>;
  findByIds(ids: string[]): Promise<T[]>;
  findWithOptions(options: FindOneOptions<T>): Promise<T | undefined>;
  findManyWithOptions(options: FindManyOptions<T>): Promise<T[]>;
  resetCache(id: string): void;
}

export interface ApplicantTypeService extends QueryService<ApplicantType> {
  findByProgramId(programId: string): Promise<ProgramApplicantType[]>;
  findByPartnerId(partnerId: string): Promise<ApplicantType[]>;
  findGlobalApplicantType(): Promise<ApplicantType>;
}
interface LinkLegacyUserService {
  relinkUser(input: LinkToLegacyUserInput): Promise<string[]>;
}

export interface AddressService extends QueryService<Address> {
  create(input: CreateAddressInput): Promise<Address>;
  findByRelation(relation: AddressRelation): Promise<Address[]>;
}

export interface AdminService extends QueryService<Admin> {
  findAndCount(
    token: AdminToken,
    filter?: AdminFilter,
    pagination?: OffsetPagination,
    sort?: Sort<AdminSortColumn>,
  ): Promise<Page<Admin>>;
  findByUserId(userId: string): Promise<Admin | undefined>;
  findUser(adminId: string): Promise<User | null>;
  create(token: AdminToken, input: CreateAdminInput): Promise<MutationResponse<Admin>>;
  update(token: AdminToken, input: UpdateAdminInput): Promise<MutationResponse<Admin>>;
  delete(token: AdminToken, input: DeleteAdminInput): Promise<MutationResponse<Admin>>;
}

export interface AnalyticsResourceService extends QueryService<AnalyticsResource> {
  findByPartnerId(partnerId: string): Promise<AnalyticsResource[]>;
}

export interface ApplicantProfileService extends QueryService<ApplicantProfile> {
  findByUserId(userId: string): Promise<ApplicantProfile | undefined>;
  create(input: { userId: string; applicantTypeId: string }): Promise<ApplicantProfile>;
  update(updates: UpdateApplicantProfileInput[]): Promise<ApplicantProfile[]>;
}

export interface ApplicationAnswerService extends QueryService<ApplicationAnswer> {
  findByVersionId(id: string): Promise<ApplicationAnswer[]>;
  findMostRecentAnswers(id: string): Promise<ApplicationAnswers>;
  update(input: { versionId: string; answers: Partial<ApplicationAnswers> }): Promise<void>;
}

export interface ApplicationVersionService
  extends QueryService<ApplicationVersion>,
    LinkLegacyUserService {
  findByApplicationId(id: string): Promise<ApplicationVersion[]>;
  findMostRecent(id: string): Promise<ApplicationVersion | undefined>;
  findMostRecentSubmission(id: string): Promise<ApplicationVersion | undefined>;
  matchConfigToApplicationAnswers(id: string): Promise<SectionResponse[] | undefined>;
  upsert(input: Partial<ApplicationVersion>): Promise<ApplicationVersion>;
}

export interface ApplicationVerificationService extends QueryService<ApplicationVerification> {
  findByVersionId(id: string): Promise<ApplicationVerification | undefined>;
  findByApplicationId(id: string): Promise<Nullable<ApplicationVerification>>;
}

export interface BulkOperationService extends QueryService<BulkOperation> {
  create(
    token: AdminToken,
    input: Pick<BulkOperation, 'operation' | 'payload'>,
  ): Promise<BulkOperation>;
  updateStatus(id: string, status: BulkOperationStatus): Promise<void>;
}

export interface ApplicationService extends QueryService<Application>, LinkLegacyUserService {
  aggregate(
    token: AdminToken,
    filter?: Omit<AggregateApplicationFilter, 'partnerId'>,
  ): Promise<AggregateApplication>;
  create(input: Pick<Application, 'caseId' | 'submitterId' | 'referralId'>): Promise<Application>;
  createVersion(
    token: LoginToken,
    applicationId: string,
    description?: string,
    versionId?: string,
  ): Promise<ApplicationVersion>;
  findAndCount(
    token: LoginToken,
    filter?: ApplicationFilter,
    pagination?: CursorPagination,
  ): Promise<Page<Application>>;
  findByCaseId(caseId: string): Promise<Application[]>;
  findBySubmitterId(id: string): Promise<Application[]>;
  update(
    ids: string[],
    updates: Partial<Pick<Application, 'addresses' | 'documents' | 'submittedAt'>>,
  ): Promise<void>;
}

export interface ApplicationScoreService extends QueryService<ApplicationScore> {
  addScore(
    applicationAnswers: Partial<ApplicationAnswers>,
    versionId: string,
  ): Promise<ApplicationScore>;
  findMostRecentScore(id: string): Promise<Nullable<ApplicationScore>>;
}

export interface AssignmentService extends QueryService<Assignment> {
  autoAssign(input: AutoAssignInput): Promise<Assignment[]>;
  upsert(input: AssignToCaseInput): Promise<Assignment[]>;
  unassignAllCasesFromAdmin(adminId: string): Promise<void>;
}

export interface CaseService extends QueryService<Case> {
  create(input: CreateCaseInput): Promise<Case>;
  findAndCount(
    token: AdminToken,
    filter?: CaseFilter,
    pagination?: OffsetPagination,
    sort?: Sort<CaseSortColumn>,
  ): Promise<Page<Case>>;
  findMetadataByCaseId(id: string): Promise<CaseMetadata | undefined>;
  findWorkflowEvents(
    token: AuthenticatedToken,
    caseId: string,
    applicationId?: string,
  ): Promise<WorkflowEvent[]>;
  removeDocuments(input: RemoveEntityDocumentsInput): Promise<MutationResponse<Case>>;
  update(ids: string[], update: Partial<Case>): Promise<Case[]>;
  uploadDocuments(
    token: AuthenticatedToken,
    input: UploadEntityDocumentsInput,
  ): Promise<MutationResponse<Case>>;
  inviteParticipant(
    token: AdminToken,
    input: InviteCaseParticipantInput,
  ): Promise<MutationResponse<Case>>;
  unlinkParticipant(
    token: AdminToken,
    input: UnlinkCaseParticipantInput,
  ): Promise<MutationResponse<Case>>;
  addTagToCase(token: AdminToken, input: CreateCaseTagInput): Promise<MutationResponse<CaseTag>>;
  removeTagFromCase(
    token: AdminToken,
    input: DeleteCaseTagInput,
  ): Promise<MutationResponse<CaseTag>>;
}

export interface CaseParticipantService extends QueryService<CaseParticipant> {
  findByCaseId(caseId: string): Promise<CaseParticipant[]>;
}

export interface CaseTagService extends QueryService<CaseTag> {
  findByCaseId(caseId: string): Promise<CaseTag[]>;
  create(input: CreateCaseTagInput): Promise<MutationResponse<CaseTag>>;
  delete(input: DeleteCaseTagInput): Promise<MutationResponse<CaseTag>>;
}

export interface ChangelogsService extends QueryService<Changelog> {
  getAll(token: AuthenticatedToken): Promise<Changelog[]>;
}
export interface CommentService extends QueryService<Comment> {
  findByCaseId(caseId: string): Promise<Comment[]>;
  create(input: Partial<Comment>[]): Promise<Comment[]>;
}

export interface CoreNotificationService extends QueryService<Notification> {
  findByCommentId(commentId: string): Promise<Notification | undefined>;
  create(input: Omit<Partial<Notification>, 'id'>): Promise<Notification>;
}

export interface ConfigurationService {
  findApplicationConfigs(input: { programId: string }): Promise<ProgramApplicationConfiguration[]>;
  findApplicantProfileConfigs(input: { partnerId: string }): Promise<
    ApplicantProfileConfiguration[]
  >;
  findProfileConfigByTypeId(input: {
    partnerId: string;
    applicantTypeId: string;
  }): Promise<ApplicantProfileConfiguration>;
  findCommunicationChannelsConfigByPartnerId(input: {
    partnerId: string;
  }): Promise<Nullable<CommunicationChannelsConfiguration>>;
  createDefaultProgramApplicationConfiguration(
    input: CreateProgramApplicationConfigurationInput,
  ): Promise<ProgramApplicationConfiguration>;
}

export interface DocumentService extends QueryService<Document>, LinkLegacyUserService {
  classify(input: Document[]): Promise<void>;
  findByRelation(relation: DocumentRelation): Promise<Document[]>;
  getSummary(id: string): Promise<Nullable<Summary>>;
  getDocumentTags(id: string): Promise<Nullable<DocumentTag[]>>;
  getDocumentFields(id: string): Promise<Nullable<DocumentField[]>>;
  getPreviewUrl(token: LoginToken, document: Document): Promise<Nullable<string>>;
  pin(input: PinDocumentInput): Promise<MutationResponse<Document>>;
  submitPredictionFeedback(
    token: AdminToken,
    input: SubmitPredictionFeedbackInput,
  ): Promise<MutationResponse<Document>>;
  upload(token: LoginToken, input: UploadDocumentsInput): Promise<Document[]>;
}

export interface EligibilityService {
  findByPartnerId(partnerId: string): Promise<Nullable<EligibilityConfig>>;
}

export interface ExternalNotificationService {
  notify<Type extends PlatformNotificationType>(
    input: SendNotificationRequest<Type>,
  ): Promise<void>;
}

export interface EnrollmentService extends QueryService<Enrollment>, LinkLegacyUserService {
  deactivate(identifiers: EnrollmentIdentifier[]): Promise<void>;
  findByApplicantId(userId: string): Promise<Enrollment[]>;
  upsert(input: UpsertEnrollmentInput): Promise<MutationResponse<Enrollment>>;
  upsertOutcome(input: UpsertEnrollmentOutcomeInput): Promise<MutationResponse<Enrollment>>;
  removeOutcome(input: RemoveEnrollmentOutcomeInput): Promise<MutationResponse<Enrollment>>;
  upsertService(input: UpsertEnrollmentServiceInput): Promise<MutationResponse<Enrollment>>;
  removeService(input: RemoveEnrollmentServiceInput): Promise<MutationResponse<Enrollment>>;
}

export interface FulfillmentService extends QueryService<Fulfillment>, Lockable {
  findAndCount(
    token: AdminToken,
    filter?: FulfillmentFilter,
    pagination?: OffsetPagination,
  ): Promise<Page<Fulfillment>>;
  findByCaseId(caseId: string): Promise<Fulfillment[]>;
  getPaymentSchedule(id: string): Promise<Nullable<Date[]>>;
  recordPaymentAttempt(payment: Payment): Promise<void>;
  save(input: Partial<Fulfillment>[]): Promise<Fulfillment[]>;
}

export interface FulfillmentMetaService extends QueryService<FulfillmentMeta> {
  findByFulfillmentId(fulfillmentId: string): Promise<Nullable<FulfillmentMeta>>;
}
export interface FundService extends QueryService<Fund> {
  calculateStats(fundId: string): Promise<FundStats>;
  findByPartnerId(partnerId: string): Promise<Fund[]>;
  resetStatsCache(fundId: string): void;
  findFundConfig(fundId: string): Promise<Nullable<FundConfig>>;
  findAndCount(
    token: AdminToken,
    filter?: FundFilter,
    pagination?: OffsetPagination,
    sort?: Sort<FundSortColumn>,
  ): Promise<Page<Fund>>;
  remove(token: AdminToken, input: { id: string }): Promise<MutationResponse<Fund>>;
  create(
    token: AdminToken,
    input: Pick<Fund, 'startingBalance' | 'name'>,
  ): Promise<MutationResponse<Fund>>;
  update(
    token: AdminToken,
    input: Pick<Fund, 'id' | 'startingBalance' | 'name'>,
  ): Promise<MutationResponse<Fund>>;
}

export interface IdentityService {
  beginSession(input: BeginSession): Promise<MutationResponse>;
  checkToken(response: Response): Promise<Nullable<AuthenticatedToken>>;
  checkPermission(
    request: Omit<CheckPermissionRequest, '_zedToken' | '_fullyConsistent'>,
  ): Promise<CanAccessResponse>;
  createAccessRequest(
    token: AdminToken,
    request: CreateAccessRequestInput,
  ): Promise<MutationResponse>;
  createRelationships(request: CreateRelationshipsRequest): Promise<MutationResponse<void>>;
  createUser(request: CreateUserRequest): Promise<CreateUserResponse>;
  endSession(input: EndSession): Promise<MutationResponse>;
  getAccessRequests(request: GetAccessRequests): Promise<AccessRequestList>;
  getUser(input: GetUserRequest): Promise<UserResponse>;
  readPortalRoles(request: ReadPortalRolesRequest): Promise<Role[]>;
  recaptcha(input: RecaptchaInput): Promise<Nullable<number>>;
  reviewAccessRequest(request: ReviewAccessRequest): Promise<MutationResponse>;
  sendMagicLink(input: SendMagicLinkRequest): Promise<MutationResponse<void>>;
  sendMagicLinkV2(input: SendMagicLinkRequest): Promise<MutationResponse<void>>;
  sendVerificationEmail(input: SendVerificationEmailRequest): Promise<MutationResponse<void>>;
  updateUser(request: UpdateUserRequest): Promise<MutationResponse>;
  verifyEmail(input: VerifyEmailRequest): Promise<MutationResponse<void>>;
}

export interface IncidentMessageService extends QueryService<IncidentMessage> {
  findAndCount(partnerId?: string): Promise<Page<IncidentMessage>>;
  softDelete(incidentId?: string): Promise<UpdateResult>;
}

export interface IncomeLimitAreaService extends QueryService<IncomeLimitArea> {
  lookup(input: FindOptionsWhere<IncomeLimitArea>): Promise<IncomeLimitArea | undefined>;
  findAndCount(filter: IncomeLimitAreaFilter): Promise<Page<IncomeLimitArea>>;
}

export interface InvitationCodeService extends QueryService<InvitationCode> {
  findByCaseParticipantId(caseParticipantId: string): Promise<InvitationCode[]>;
}

export interface LinkAttemptService extends QueryService<LinkAttempt> {
  findByCaseParticipantId(caseParticipantId: string): Promise<LinkAttempt[]>;
}

export interface MessagingService {
  publish<T extends Stringifiable>(input: PublishInput<T>): Promise<string>;
  publishBulk<T extends Stringifiable>(input: BulkPublishInput<T>): Promise<BulkPublishResult>;
}

export interface NoteService extends QueryService<Note> {
  add(token: AdminToken, input: AddNote): Promise<MutationResponse<Note>>;
  addBulk(
    token: AdminToken,
    input: { relationType: NoteRelationType; relationIds: string[]; content: string },
  ): Promise<Note[]>;
  findByRelation(relation: NoteRelation): Promise<Note[]>;
  update(token: AdminToken, input: UpdateNote): Promise<MutationResponse<Note>>;
}

export interface OutcomeService extends QueryService<Outcome> {
  findByProgramId(programId: string): Promise<Outcome[]>;
}

export interface PartnerService extends QueryService<Partner> {
  findAndCount(
    filter?: Pick<Partner, 'externalId'>,
    pagination?: CursorPagination,
  ): Promise<Page<Partner>>;
  getAvailableRoles(partnerId: string): Role[];
  hasFeature({ partnerId, feature }: { partnerId: string; feature: FeatureName }): Promise<boolean>;
}

export interface PartnerIncidentService extends QueryService<PartnerIncident> {
  remove(token: AdminToken, input: RemoveIncidentInput): Promise<MutationResponse<PartnerIncident>>;
  create(token: AdminToken, input: CreateIncidentInput): Promise<MutationResponse<PartnerIncident>>;
}

export interface PartnerFeatureService extends QueryService<PartnerFeature> {
  findByPartnerId(partnerId: string): Promise<PartnerFeature[]>;
}

export interface PartnerWhitelabelingService extends QueryService<PartnerWhitelabeling> {
  findByPartnerId(partnerId: string): Promise<PartnerWhitelabeling | undefined>;
}

export interface PayeeService {
  findAccountByPayeeId(payeeId: string): Promise<Nullable<BankAccount>>;
  findPayeeMailingAddress(payee: Payee): Promise<Nullable<Address>>;
  upsertBankAccount(payee: PayeeInput): Promise<MutationResponse<Payee>>;
}

export interface PaymentService extends QueryService<Payment>, LinkLegacyUserService {
  create(token: AdminToken, input: CreatePaymentInput): Promise<MutationResponse<Payment>>;
  createRelationships(paymentId: string): Promise<MutationResponse<void>>;
  createRelationshipsByFulfillmentId(fulfillmentId: string): Promise<MutationResponse<void>>;
  update(token: AdminToken, input: UpdatePaymentInput): Promise<MutationResponse<Payment>>;
  remove(paymentId: string): Promise<MutationResponse>;
  save(input: Partial<Payment>[]): Promise<Partial<Payment>[]>;
  findByVendorId(
    vendorId: string,
    pagination?: OffsetPagination,
    sort?: Sort<PaymentSortColumn>,
  ): Promise<Page<Payment>>;
  aggregate(token: AdminToken, filter?: AggregatePaymentFilter): Promise<AggregatePayments>;
  aggregateByApplicantId(applicantId: string): Promise<AggregatePayments>;
  aggregateByPayeeId(payeeId: string): Promise<AggregatePayments>;
}

export interface PaymentPatternService extends QueryService<PaymentPattern> {
  buildPattern(
    input: PaymentPatternInput,
  ): Promise<Pick<PaymentPattern, 'amount' | 'count' | 'pattern' | 'start' | 'totalAmount'>>;
  deletePattern(id: string): Promise<DeleteResult>;
  findByFulfillmentId(fulfillmentId: string): Promise<Nullable<PaymentPattern>>;
  upsertPattern(input: PaymentPatternInput & { fulfillmentId: string }): Promise<PaymentPattern>;
}

export interface ProfileAnswerService extends QueryService<ProfileAnswer> {
  findByProfileId(profileId: string): Promise<Partial<ApplicationAnswers>>;
  update(updates: { profileId: string; answers: CreateAnswerInput[] }[]): Promise<void>;
}

export interface ProgramFeatureService extends QueryService<ProgramFeature> {
  findByProgramId(programId: string): Promise<ProgramFeature[]>;
  hasFeature(input: CheckFeature): Promise<boolean>;
  hasFeatures(input: { programId: string; features: FeatureName[] }): Promise<boolean>;
  create(input: { programId: string; features: FeatureName[] }): Promise<ProgramFeature[]>;
  resetProgramFeaturesCache(programId: string): void;
}

export interface ProgramFundService extends QueryService<ProgramFund> {
  deleteById(programFundId: string): Promise<DeleteResult>;
  create(input: Pick<ProgramFund, 'fundId' | 'programId'>): Promise<ProgramFund>;
}

export interface ProgramReferralService extends QueryService<ProgramReferral> {
  save(input: Partial<ProgramReferral>[]): Promise<ProgramReferral[]>;
  create(token: AdminToken, input: CreateReferralInput): Promise<MutationResponse<ProgramReferral>>;
  findByUserId(userId: string): Promise<ProgramReferral[]>;
  aggregateApplicantsReferred(token: AdminToken): Promise<AggregateApplicantsReferred>;
}

export interface ProgramDocumentService extends QueryService<ProgramDocument> {
  save(input: Partial<ProgramDocument>): Promise<ProgramDocument>;
  findMostRecent(programId: string): Promise<ProgramDocument | undefined>;
}

export interface ProgramService extends QueryService<Program> {
  calculateStats(programId: string): Promise<ProgramStats>;
  findAndCount(
    token: LoginToken,
    filter?: Pick<Program, 'id'>,
    pagination?: CursorPagination,
  ): Promise<Page<Program>>;
  findByPartnerId(partnerId: string): Promise<Program[]>;
  getProgramWorkflow(programId: string, partnerId: string): Promise<Workflow>;
  getDocumentLabels(programId: string): Promise<ActiveLabel[]>;
  getProgramNotifications(programId: string): Promise<NotificationConfig[]>;
  resetProgramFundsStatsCache(programId: string): void;
  resetCaseCountsCache(programId: string): void;
  save(program: Partial<Program>): Promise<Program>;
  update(token: AdminToken, input: UpdateProgramsInput): Promise<MutationResponse<Program[]>>;
  resetPartnerProgramsCache(partnerId: string): void;
}

export interface ServiceService extends QueryService<Service> {
  findByProgramId(programId: string): Promise<Service[]>;
}

export interface SavedViewService extends QueryService<SavedView> {
  findByAuthorOrPublic(filter: { partnerId: string; authorId: string }): Promise<SavedView[]>;
  findByPartnerId(partnerId: string): Promise<SavedView[]>;
  create(token: AdminToken, input: CreateSavedViewInput): Promise<MutationResponse<SavedView>>;
  update(input: UpdateSavedViewInput): Promise<MutationResponse<SavedView>>;
  delete(id: string): Promise<MutationResponse<void>>;
}

export interface TagsService {
  findById(id: string): Promise<Tag>;
  findByPartnerId(partnerId: string): Promise<Tag[]>;
  create(token: AdminToken, input: CreateTagsInput): Promise<MutationResponse<Tag[]>>;
  update(token: AdminToken, input: UpdateTagInput): Promise<MutationResponse<Tag>>;
  delete(token: AdminToken, input: DeleteTagsInput): Promise<MutationResponse<Tag[]>>;
}

export interface TaxFormService extends QueryService<TaxForm> {
  saveTaxForm(input: Omit<TaxForm, 'id'>): Promise<void>;
  findByUserId(userId: string): Promise<TaxForm[]>;
}

export interface UserService extends QueryService<User> {
  create(input: Omit<Partial<User>, 'id'>): Promise<User>;
  deactivate(id: string): Promise<void>;
  findAndCount(
    token: AdminToken,
    filter?: UserFilter,
    pagination?: OffsetPagination,
    sort?: Sort<UserSortColumn>,
  ): Promise<Page<User>>;
  getIdentityUserByAdminUserId(id: string): Promise<IdentityUser | null>;
  update(input: Partial<User>): Promise<User>;
}

export interface VendorService extends QueryService<Vendor> {
  create(token: AdminToken, input: CreateVendorInput): Promise<MutationResponse<Vendor>>;
  delete(id: string): Promise<void>;
  findAndCount(
    token: AdminToken,
    filter?: VendorFilter,
    pagination?: OffsetPagination,
    sort?: Sort<VendorSortColumn>,
  ): Promise<Page<Vendor>>;
  listTypes(token: AdminToken): Promise<VendorType[]>;
  removeDocuments(input: RemoveVendorDocumentsInput): Promise<MutationResponse<Vendor>>;
  uploadDocuments(
    token: AdminToken,
    input: UploadVendorDocumentsInput,
  ): Promise<MutationResponse<Vendor>>;
  update(token: AdminToken, input: UpdateVendorInput): Promise<MutationResponse<Vendor>>;
}

export interface VerificationService {
  findConfigurationByProgramId(id: string): Promise<VerificationConfiguration[]>;
  verify(input: ApplicationSubmissionEvent): void;
  uploadFile(input: UploadVerificationFileEvent): void;
  upsertLookupConfig(input: UpsertLookupConfigInput): void;
}

export interface WorkflowEventService extends QueryService<WorkflowEvent> {
  create(
    token: AdminToken,
    input: CreateWorkflowEventInput | CreateWorkflowEventInput[],
  ): Promise<WorkflowEvent[]>;
  createOnBehalfOf(
    input: CreateWorkflowEventInput | CreateWorkflowEventInput[],
    userId: string,
  ): Promise<WorkflowEvent[]>;
  createOnBehalfOfDependency(
    input: CreateWorkflowEventInput | CreateWorkflowEventInput[],
  ): Promise<WorkflowEvent[]>;
  findByEntityId(token: AuthenticatedToken, id: string): Promise<WorkflowEvent[]>;
  registerBulkOperation(id: string): void;
  summarize(
    token: AdminToken,
    filter: Omit<WorkflowSummaryFilter, 'partnerId'>,
  ): Promise<WorkflowSummary>;
}

export interface VendorTypeService extends QueryService<VendorType> {
  findByRelation(relation: VendorTypeRelation): Promise<VendorType[]>;
}

// Doctopus Services
export interface ActiveLabelService extends QueryService<ActiveLabel> {}
export interface PredictionFeedbackService extends QueryService<Feedback> {
  findByPredictionId({
    predictionId,
    token: { adminId },
  }: { predictionId: string; token: AdminToken }): Promise<Feedback[]>;
}

export interface SearchService {
  search: <I extends Index>(
    token: AdminToken,
    args: SearchArguments<I>,
  ) => Promise<SearchResult<I>>;
}

export interface Services {
  admins: AdminService;
  addresses: AddressService;
  analyticsResources: AnalyticsResourceService;
  applications: ApplicationService;
  applicationAnswers: ApplicationAnswerService;
  applicationAnswerNotes: IApplicationAnswerNoteService;
  applicationAnswerReviews: IApplicationAnswerReviewService;
  applicationScores: ApplicationScoreService;
  applicationVersions: ApplicationVersionService;
  applicationVerifications: ApplicationVerificationService;
  applicantProfiles: ApplicantProfileService;
  applicantTypes: ApplicantTypeService;
  assignments: AssignmentService;
  bulkOperations: BulkOperationService;
  cases: CaseService;
  caseParticipants: CaseParticipantService;
  caseTags: CaseTagService;
  changelogs: ChangelogsService;
  config: ConfigurationService;
  comments: CommentService;
  documents: DocumentService;
  eligibility: EligibilityService;
  externalNotifications: ExternalNotificationService;
  enrollments: EnrollmentService;
  fulfillments: FulfillmentService;
  fulfillmentMeta: FulfillmentMetaService;
  funds: FundService;
  identity: IdentityService;
  incidentMessages: IncidentMessageService;
  incomeLimitAreas: IncomeLimitAreaService;
  invitationCodes: InvitationCodeService;
  linkAttempts: LinkAttemptService;
  notifications: CoreNotificationService;
  messaging: MessagingService;
  notes: NoteService;
  outcomes: OutcomeService;
  partners: PartnerService;
  partnerIncidents: PartnerIncidentService;
  partnerFeatures: PartnerFeatureService;
  payees: PayeeService;
  payments: PaymentService;
  paymentPatterns: PaymentPatternService;
  preset: IPresetService;
  programs: ProgramService;
  programDocuments: ProgramDocumentService;
  programFeatures: ProgramFeatureService;
  programFunds: ProgramFundService;
  programReferrals: ProgramReferralService;
  profileAnswers: ProfileAnswerService;
  search: SearchService;
  services: ServiceService;
  savedViews: SavedViewService;
  tags: TagsService;
  taxForms: TaxFormService;
  users: UserService;
  vendors: VendorService;
  vendorTypes: VendorTypeService;
  verification: VerificationService;
  whitelabeling: PartnerWhitelabelingService;
  workflowEvents: WorkflowEventService;

  // Doctopus services
  activeLabels: ActiveLabelService;
  predictionFeedback: PredictionFeedbackService;
}
