import { MeterProvider } from '@bybeam/infrastructure-lib/instrumentation';
import { Operations } from '@platform-api/@types/operations.js';
import { Services } from '@platform-api/@types/services.js';
import ClaimRepository from '../../repositories/external/ClaimRepository/index.js';
import ClaimFundsOperation from './ClaimFundsOperation.js';
import HandlePaymentCallbackOperation from './HandlePaymentCallbackOperation.js';
import IssueFundsOperation from './IssueFundsOperation.js';
import ResetPaymentOperation from './ResetPaymentOperation.js';
import { SendPaymentValidator } from './SendPaymentValidator.js';
import { TransactionResponseHandler } from './TransactionResponseHandler.js';
import UpsertBankAccountOperation from './UpsertBankAccountOperation.js';
import FindAndSaveMailingAddressOperation from './FindAndSaveMailingAddressOperation.js';
import BulkIssueFundOperation from './BulkIssueFundsOperation.js';

export const build = (
  {
    applications,
    bulkOperations,
    cases,
    externalNotifications,
    fulfillments,
    funds,
    messaging,
    payees,
    payments,
    programs,
    programFeatures,
    users,
    vendors,
    workflowEvents,
  }: Services,
  meterProvider: MeterProvider,
): Operations['payments'] => {
  const findAndSaveMailingAddress = new FindAndSaveMailingAddressOperation({
    applications,
    payments,
    payees,
  });

  const reset = new ResetPaymentOperation({
    cases,
    externalNotifications,
    fulfillments,
    messaging,
    meterProvider,
    payments,
    workflowEvents,
  });
  const handleCallback = new HandlePaymentCallbackOperation({
    externalNotifications,
    payments,
    messaging,
    resetPayment: reset,
    workflowEvents,
  });

  const claim = new ClaimRepository();
  const responseHandler = new TransactionResponseHandler({
    cases,
    fulfillments,
    funds,
    payments,
    programs,
    programFeatures,
  });
  const validator = new SendPaymentValidator({ fulfillments });
  const claimFunds = new ClaimFundsOperation({
    claim,
    externalNotifications,
    fulfillments,
    messaging,
    payments,
    responseHandler,
    validator,
  });
  const issueFunds = new IssueFundsOperation({
    applications,
    claim,
    externalNotifications,
    findAndSaveMailingAddress,
    fulfillments,
    messaging,
    payees,
    payments,
    responseHandler,
    validator,
    workflowEvents,
  });
  const upsertBankAccount = new UpsertBankAccountOperation({ payees, programs, users, vendors });

  const bulkIssueFunds = new BulkIssueFundOperation({ fulfillments, messaging, bulkOperations });
  return {
    bulkIssueFunds,
    claimFunds,
    issueFunds,
    handleCallback,
    reset,
    upsertBankAccount,
    findAndSaveMailingAddress,
  };
};
