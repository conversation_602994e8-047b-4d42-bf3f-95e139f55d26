import {
  BulkMutationResponse,
  Fulfillment,
  BulkOperationType,
  BulkResponseError,
  Nullable,
  TopicName,
  BulkIssueFundsInput,
} from '@bybeam/platform-types';
import { AdminToken } from '@platform-api/@types/authentication.js';
import { Services } from '@platform-api/@types/services.js';
import BulkOperation from '../AbstractBulkOperation.js';
import { PaymentEventType } from '@bybeam/common-proto';

export default class BulkIssueFundOperation extends BulkOperation<
  Pick<Services, 'bulkOperations' | 'fulfillments' | 'messaging'>,
  BulkIssueFundsInput,
  Fulfillment
> {
  protected get operation(): BulkOperationType {
    return BulkOperationType.IssueFunds;
  }

  protected async validate(
    input: BulkIssueFundsInput,
    fulfillments: Fulfillment[],
  ): Promise<BulkResponseError[]> {
    // Add any custom validation logic here if needed
    return [];
  }

  protected async act(
    token: AdminToken,
    input: BulkIssueFundsInput,
    fulfillments: Fulfillment[],
  ): Promise<Nullable<BulkMutationResponse<Fulfillment>>> {
    this.log('info', `Process starts ... for (${input.ids?.length}) fulfillments`);

    await this.dependencies.messaging.publishBulk({
      topicName: TopicName.Payments,
      messages: fulfillments.map((fulfillment) => ({
        fulfillmentId: fulfillment.id,
        caseId: fulfillment.caseId,
        eventType: PaymentEventType.PAYMENT_TRANSACTION_REQUEST,
        timestamp: new Date().toISOString(),
        actor: { id: token.identityUserId },
        _applicationId: 'applicationId',
        // TODO: Will Add details
        _details: 'details',
      })),
    });
    return;
  }

  protected async findRecordsByIds(ids: string[]): Promise<Fulfillment[]> {
    return this.dependencies.fulfillments.findByIds(ids);
  }

  protected async resetCache(id: string): Promise<void> {
    this.dependencies.fulfillments.resetCache(id);
  }
}
