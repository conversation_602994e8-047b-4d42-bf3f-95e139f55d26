import {
  BulkMutationResponse,
  BulkOperationStatus,
  BulkOperationType,
  BulkResponseError,
  Nullable,
} from '@bybeam/platform-types';
import AbstractOperation from './AbstractOperation.js';
import { Services } from '../@types/services.js';
import { StatusCodes } from 'http-status-codes';
import { bulkError, bulkSuccess } from '../utils/response.js';
import { AdminToken } from '../@types/authentication.js';

export default abstract class AbstractBulkOperation<
  Dependencies extends Pick<Services, 'bulkOperations'>,
  Input extends { ids: string[] },
  Entity extends { id: string },
> extends AbstractOperation<Dependencies, Input, BulkMutationResponse<Entity>, AdminToken> {
  protected bulkOperationId: string;

  protected abstract validate(input: Input, records: Entity[]): Promise<BulkResponseError[]>;
  protected abstract act(
    token: AdminToken,
    input: Input,
    records: Entity[],
  ): Promise<Nullable<BulkMutationResponse<Entity>>>;
  protected abstract get operation(): BulkOperationType;
  protected get limit(): number | undefined {
    return undefined;
  }

  // Subclass must implement this to fetch entities by IDs
  protected abstract findRecordsByIds(ids: string[]): Promise<Entity[]>;
  // Subclass must implement this to reset cache if needed (can be a no-op)
  protected abstract resetCache(id: string): void | Promise<void>;

  public async run(token: AdminToken, input: Input): Promise<BulkMutationResponse<Entity>> {
    try {
      const { id: bulkOperationId } = await this.dependencies.bulkOperations.create(token, {
        operation: this.operation,
        payload: { ids: input.ids },
      });
      this.bulkOperationId = bulkOperationId;

      const { records, error } = await this.validateBulkAction(input);
      if (error) {
        await this.dependencies.bulkOperations.updateStatus(
          this.bulkOperationId,
          BulkOperationStatus.ValidationFailed,
        );
        return error;
      }

      const validationErrors = await this.validate(input, records);
      if (validationErrors.length) {
        await this.dependencies.bulkOperations.updateStatus(
          this.bulkOperationId,
          BulkOperationStatus.ValidationFailed,
        );
        return bulkError({ statusCode: StatusCodes.BAD_REQUEST, errors: validationErrors });
      }

      await this.dependencies.bulkOperations.updateStatus(
        this.bulkOperationId,
        BulkOperationStatus.InProgress,
      );
      const response = await this.act(token, input, records);
      if (response) {
        await this.dependencies.bulkOperations.updateStatus(
          this.bulkOperationId,
          response.metadata.status >= 400
            ? BulkOperationStatus.Failed
            : BulkOperationStatus.Completed,
        );
        return response;
      }

      for (const id of input.ids) await this.resetCache(id);
      await this.dependencies.bulkOperations.updateStatus(
        this.bulkOperationId,
        BulkOperationStatus.Completed,
      );
      return bulkSuccess({
        recordIds: input.ids,
        records: await this.findRecordsByIds(input.ids),
      });
    } catch (error) {
      this.log('error', 'unexpected error >', { error });
      await this.dependencies.bulkOperations.updateStatus(
        this.bulkOperationId,
        BulkOperationStatus.Failed,
      );
      return bulkError();
    }
  }

  private async validateBulkAction({ ids }: Input): Promise<{
    records: Entity[];
    error?: BulkMutationResponse<Entity>;
  }> {
    if (ids.length === 0)
      return {
        records: [],
        error: bulkError({
          statusCode: StatusCodes.BAD_REQUEST,
          errors: [{ id: '', message: 'No ids provided' }],
        }),
      };

    if (this.limit !== undefined && ids.length > this.limit)
      return {
        records: [],
        error: bulkError({
          statusCode: StatusCodes.BAD_REQUEST,
          errors: [
            { id: '', message: `This action cannot be taken on more than ${this.limit} items.` },
          ],
        }),
      };

    const records = await this.findRecordsByIds(ids);
    const existingIds = new Set(records.map((r: Entity) => r.id));
    const missingIds = ids.filter((id) => !existingIds.has(id));

    if (missingIds.length)
      return {
        records,
        error: bulkError({
          statusCode: StatusCodes.NOT_FOUND,
          errors: missingIds.map((id) => ({ id, message: 'Could not find record with id' })),
        }),
      };

    return { records };
  }
}
