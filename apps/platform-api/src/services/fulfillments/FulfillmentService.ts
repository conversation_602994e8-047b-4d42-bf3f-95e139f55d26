import {
  FulfillmentRepository,
  LockRepository,
  PaymentRepository,
} from '@platform-api/@types/repositories/index.js';
import { FulfillmentService as IFulfillmentService } from '@platform-api/@types/services.js';
import { In } from 'typeorm';
import { ClaimRepository, ClaimRepositoryResponseStatus } from '../../@types/claim/index.js';
import dayjs from '../../utils/dayJsConfig.js';
import { logger } from '../../utils/logger.js';
import { pick } from '../../utils/set.js';
import QueryServiceImplementation from '../QueryService.js';
import ManyToOneDataLoader from '../utils/ManyToOneDataLoader.js';
import { isRecurringSchedule } from '../utils/payments.js';
import { AdminToken, isAdminToken } from '../../@types/authentication.js';
import {
  Fulfillment,
  FulfillmentFilter,
  OffsetPagination,
  Page,
  Nullable,
  Payment,
  PaymentStatus,
} from '@bybeam/platform-types';

export default class FulfillmentService
  extends QueryServiceImplementation<Fulfillment, FulfillmentRepository>
  implements IFulfillmentService
{
  private claimRepository: ClaimRepository;
  private paymentRepository: PaymentRepository;

  constructor(
    repository: FulfillmentRepository,
    claimRepository: ClaimRepository,
    lockRepository: LockRepository,
    paymentRepository: PaymentRepository,
  ) {
    super(repository, lockRepository);
    this.claimRepository = claimRepository;
    this.paymentRepository = paymentRepository;
  }

  private readonly caseToFulfillmentDataloader = new ManyToOneDataLoader(
    (fulfillment: Fulfillment) => fulfillment.caseId,
    async (ids: string[]) =>
      this.repository.find({ where: { caseId: In(ids) }, relations: ['fund'] }),
  );

  public resetCache(id: string): void {
    super.resetCache(id);
    this.caseToFulfillmentDataloader.clear(id);
  }

  public async findByCaseId(caseId: string): Promise<Fulfillment[]> {
    return this.caseToFulfillmentDataloader.load(caseId);
  }

  public async findAndCount(
    token: AdminToken,
    filter: FulfillmentFilter,
    pagination: OffsetPagination = { take: 50, page: 0 },
  ): Promise<Page<Fulfillment>> {
    if (!isAdminToken(token)) {
      return this.findAndCountInternal(
        {
          id: In(filter?.ids || []),
        },
        pagination,
        ['case'],
      );
    }
    const { ids, count } = await this.repository.findAndCountIds({
      filter,
      pagination,
    });
    const nodes =
      (await this.findManyWithOptions({
        where: { id: In(ids) },
        relations: ['case', 'fund'],
      })) ?? [];
    logger.info({ nodes, ids }, 'dbz');
    // const unsortedNodes = await this.findManyWithOptions({
    //   where: { id: In(ids) },
    //   relations: ['case', 'fund'],
    // });
    // const nodes = ids.map((id) =>
    //   unsortedNodes.find(({ id: nodeId }) => id === nodeId),
    // ) as Fulfillment[];
    return { nodes, pageInfo: { count } };
  }

  public async getPaymentSchedule(id: string): Promise<Nullable<Date[]>> {
    try {
      const { payments, ...fulfillment } = await this.repository.findOneOrFail({
        where: { id },
        relations: ['payments', 'fund', 'paymentPattern'],
      });
      if (!isRecurringSchedule(fulfillment)) return;
      if (!payments || payments.length === 0)
        throw new Error(`Missing payments for fulfillment ${id}`);

      if (payments.every(({ scheduledFor }) => !!scheduledFor))
        return (payments.map(({ scheduledFor }) => scheduledFor) as Date[]).sort(
          (d1, d2) => d1?.getTime() - d2?.getTime(),
        );

      const schedule = await this.claimRepository.schedulePayments(
        // Before the payments are scheduled there will just be a single PENDING payment for the complete amount
        { ...payments[0], fulfillment },
      );
      if (schedule.status !== ClaimRepositoryResponseStatus.Success)
        throw new Error(schedule.error.message);
      return schedule.payload.map(({ scheduledFor }) => scheduledFor);
    } catch (error) {
      logger.error({ error }, `${this.constructor.name}.getPaymentSchedule: unexpected error >`);
      return [];
    }
  }

  public async recordPaymentAttempt(payment: Payment): Promise<void> {
    const partialCopy = pick<Payment>(payment, [
      'fulfillmentId',
      'amount',
      'method',
      'payeeId',
      'payeeType',
      'mailingAddressType',
      'mailingAddress',
    ]);
    await this.paymentRepository.insert({
      ...partialCopy,
      deactivatedAt: dayjs.utc().toDate(),
      status: PaymentStatus.Authorized,
    });
  }

  public async save(input: Partial<Fulfillment>[]): Promise<Fulfillment[]> {
    return this.repository.save(input);
  }
}
