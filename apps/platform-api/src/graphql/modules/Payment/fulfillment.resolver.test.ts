import { mockLoginToken, mockAdminToken } from '@platform-api-test/mocks.js';
import { AdminContext, AuthenticatedContext } from '@platform-api/@types/graphql.js';
import { GraphQLResolveInfo } from 'graphql';
import {
  FeatureName,
  PaymentMethod,
  AddressInput,
  BankAccount,
  Fulfillment,
} from '@bybeam/platform-types';
import fulfillmentResolvers from './fulfillment.resolver.js';

describe('Fulfillment Resolvers', () => {
  describe('Mutation.fulfillment', () => {
    it('should return ProgramMutation parent object', () => {
      const result = fulfillmentResolvers.Mutation.fulfillment();
      expect(result).toEqual({ _type: 'FulfillmentMutations' });
    });
  });
  describe('Field Resolvers', () => {
    const mockFulfillment = {
      id: 'mockFulfillmentId',
      caseId: 'mockCaseId',
      fundId: 'mockFundId',
    };
    describe('case', () => {
      it('returns the fulfillment case', async () => {
        const findByIdFn = vi.fn();
        await fulfillmentResolvers.Fulfillment.case(
          mockFulfillment as unknown as Fulfillment,
          {},
          {
            services: {
              cases: {
                findById: findByIdFn.mockResolvedValueOnce([{ id: mockFulfillment.caseId }]),
              },
              partners: { hasFeature: vi.fn().mockResolvedValueOnce(false) },
            },
          } as unknown as AdminContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(findByIdFn).toBeCalledTimes(1);
        expect(findByIdFn).toBeCalledWith(mockFulfillment.caseId);
      });
    });
    describe('fund', () => {
      it('returns the fund', async () => {
        const findByIdFn = vi.fn();
        await fulfillmentResolvers.Fulfillment.fund(
          mockFulfillment as unknown as Fulfillment,
          {},
          {
            services: {
              funds: {
                findById: findByIdFn.mockResolvedValueOnce([{ id: mockFulfillment.fundId }]),
              },
              partners: { hasFeature: vi.fn().mockResolvedValueOnce(false) },
            },
          } as unknown as AdminContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(findByIdFn).toBeCalledTimes(1);
        expect(findByIdFn).toBeCalledWith(mockFulfillment.fundId);
      });
    });
    describe('fulfillmentMeta', () => {
      const fulfillmentMeta = { id: 'mockMetaId' };
      it('returns the fulfillmentMeta if entity has it', async () => {
        const result = fulfillmentResolvers.Fulfillment.fulfillmentMeta(
          {
            id: mockFulfillment.id,
            fulfillmentMeta,
          } as unknown as Fulfillment,
          {},
          {} as unknown as AdminContext,
          {} as unknown as GraphQLResolveInfo,
        );
        await expect(result).resolves.toStrictEqual(fulfillmentMeta);
      });
      it('retrieves the fulfillmentMeta from the fulfillmentMeta service if it is not included in the fulfillment entity', async () => {
        const findByFulfillmentIdFn = vi.fn();
        await fulfillmentResolvers.Fulfillment.fulfillmentMeta(
          {
            id: 'mockFulfillmentId',
          } as unknown as Fulfillment,
          {},
          {
            services: {
              fulfillmentMeta: {
                findByFulfillmentId: findByFulfillmentIdFn.mockReturnValueOnce(fulfillmentMeta),
              },
              partners: { hasFeature: vi.fn().mockResolvedValueOnce(false) },
            },
          } as unknown as AdminContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(findByFulfillmentIdFn).toBeCalledTimes(1);
        expect(findByFulfillmentIdFn).toBeCalledWith(mockFulfillment.id);
      });
    });
    describe('paymentPattern', () => {
      const paymentPattern = { id: 'mockPaymentPatternId' };
      it('returns the paymentPattern if entity has it', async () => {
        const result = fulfillmentResolvers.Fulfillment.paymentPattern(
          {
            id: mockFulfillment.id,
            paymentPattern,
          } as unknown as Fulfillment,
          {},
          {} as unknown as AdminContext,
          {} as unknown as GraphQLResolveInfo,
        );
        await expect(result).resolves.toStrictEqual(paymentPattern);
      });
      it('retrieves the paymentPattern from the paymentPattern service if it is not included in the fulfillment entity', async () => {
        const findByFulfillmentIdFn = vi.fn();
        await fulfillmentResolvers.Fulfillment.paymentPattern(
          mockFulfillment as unknown as Fulfillment,
          {},
          {
            services: {
              paymentPatterns: {
                findByFulfillmentId: findByFulfillmentIdFn.mockReturnValueOnce(paymentPattern),
              },
              partners: { hasFeature: vi.fn().mockResolvedValueOnce(false) },
            },
          } as unknown as AdminContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(findByFulfillmentIdFn).toBeCalledTimes(1);
        expect(findByFulfillmentIdFn).toBeCalledWith(mockFulfillment.id);
      });
    });
    describe('schedule', () => {
      it('returns the schedule belong to a fulfillment', async () => {
        const getPaymentScheduleFn = vi.fn();
        await fulfillmentResolvers.Fulfillment.schedule(
          mockFulfillment as unknown as Fulfillment,
          {},
          {
            services: {
              fulfillments: {
                getPaymentSchedule: getPaymentScheduleFn.mockReturnValueOnce([]),
              },
              partners: { hasFeature: vi.fn().mockResolvedValueOnce(false) },
            },
          } as unknown as AdminContext,
          {} as unknown as GraphQLResolveInfo,
        );
        expect(getPaymentScheduleFn).toBeCalledTimes(1);
        expect(getPaymentScheduleFn).toBeCalledWith(mockFulfillment.id);
      });
    });
  });
  describe('Mutations', () => {
    const claimInputs = {
      claimCheck: { address: { addressLine1: '1 Main St' } as AddressInput },
      claimDirectDeposit: { bankAccount: { accountNumber: '6789' } as BankAccount },
      claimPhysicalCard: { address: { addressLine1: '1 Main St' } as AddressInput },
      claimVirtualCard: { address: { addressLine1: '1 Main St' } as AddressInput },
      claimZelle: {},
    };
    const claimMutations = [
      'claimCheck',
      'claimDirectDeposit',
      'claimPhysicalCard',
      'claimVirtualCard',
      'claimZelle',
    ] as const;

    test.each(claimMutations)('%p: throws if there is no authenticated user', (mutation) => {
      const resolver = fulfillmentResolvers.FulfillmentMutations[mutation];
      expect(() =>
        resolver(
          undefined,
          // @ts-ignore
          { input: { id: 'mock-fulfillment-id', ...claimInputs[mutation] } },
          {} as AuthenticatedContext,
          {} as GraphQLResolveInfo,
        ),
      ).toThrow();
    });

    test.each(claimMutations)(
      '%p: throws if the user is not the payee of the fulfillment',
      async (mutation) => {
        const resolver = fulfillmentResolvers.FulfillmentMutations[mutation];
        await expect(
          resolver(
            undefined,
            // @ts-ignore
            { input: { id: 'mock-fulfillment-id', ...claimInputs[mutation] } },
            {
              services: {
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: false }) },
                fulfillments: {
                  findWithOptions: vi.fn().mockResolvedValueOnce({
                    payments: [{ payeeId: 'other-user-id' }],
                  }),
                },
              },
              token: mockLoginToken,
            } as unknown as AuthenticatedContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow('User is not the owner of this entity');
      },
    );

    test.each(claimMutations)(
      '%p: throws if the fulfillment belongs to a different partner',
      async (mutation) => {
        const resolver = fulfillmentResolvers.FulfillmentMutations[mutation];
        await expect(
          resolver(
            undefined,
            // @ts-ignore
            { input: { id: 'mock-fulfillment-id', ...claimInputs[mutation] } },
            {
              services: {
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
                fulfillments: {
                  findWithOptions: vi.fn().mockResolvedValue({
                    case: { program: { partnerId: 'other-partner-id' } },
                    payments: [{ payeeId: mockLoginToken.userId }],
                  }),
                },
              },
              token: mockLoginToken,
            } as unknown as AuthenticatedContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow('User is trying to take action on an entity in a different partner');
      },
    );

    test.each([
      ['claimCheck', FeatureName.PaymentsCheck],
      ['claimDirectDeposit', FeatureName.PaymentsDirectDeposit],
      ['claimPhysicalCard', FeatureName.PaymentsPrepaidCard],
      ['claimVirtualCard', FeatureName.PaymentsPrepaidCard],
      ['claimZelle', FeatureName.PaymentsZelle],
    ] as const)(
      '%p: throws if the Claim Funds and %p features are not enabled',
      async (mutation, feature) => {
        const resolver = fulfillmentResolvers.FulfillmentMutations[mutation];
        const checkProgramFeatures = vi.fn().mockResolvedValueOnce(false);
        await expect(
          resolver(
            undefined,
            // @ts-ignore
            { input: { id: 'mock-fulfillment-id', ...claimInputs[mutation] } },
            {
              services: {
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
                fulfillments: {
                  findWithOptions: vi.fn().mockResolvedValue({
                    case: {
                      programId: 'mock-program-id',
                      program: { partnerId: mockLoginToken.partnerId },
                    },
                    payments: [{ payeeId: mockLoginToken.userId }],
                  }),
                },
                programFeatures: { hasFeatures: checkProgramFeatures },
              },
              token: mockLoginToken,
            } as unknown as AuthenticatedContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow(`All features Payments: Claim Funds,${feature} are not enabled`);
        expect(checkProgramFeatures).toHaveBeenCalledWith({
          programId: 'mock-program-id',
          features: [FeatureName.PaymentsClaimFunds, feature],
        });
      },
    );

    test.each([
      ['claimCheck', PaymentMethod.Check],
      ['claimDirectDeposit', PaymentMethod.DirectDeposit],
      ['claimPhysicalCard', PaymentMethod.PhysicalCard],
      ['claimVirtualCard', PaymentMethod.VirtualCard],
      ['claimZelle', PaymentMethod.Zelle],
    ] as const)(
      '%p: calls the FulfillmentService.claimFunds method if all conditions pass',
      async (mutation, method) => {
        const resolver = fulfillmentResolvers.FulfillmentMutations[mutation];
        const claimFundsFn = vi.fn().mockResolvedValueOnce({ metadata: { status: 200 } });
        const response = await resolver(
          undefined,
          // @ts-ignore
          { input: { id: 'mock-fulfillment-id', ...claimInputs[mutation] } },
          {
            operations: { payments: { claimFunds: { run: claimFundsFn } } },
            services: {
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              fulfillments: {
                findWithOptions: vi.fn().mockResolvedValue({
                  case: {
                    programId: 'mock-program-id',
                    program: { partnerId: mockLoginToken.partnerId },
                  },
                  payments: [{ payeeId: mockLoginToken.userId }],
                }),
              },
              programFeatures: { hasFeatures: vi.fn().mockResolvedValueOnce(true) },
            },
            token: mockLoginToken,
          } as unknown as AuthenticatedContext,
          {} as GraphQLResolveInfo,
        );
        expect(response).toEqual({ metadata: { status: 200 } });
        expect(claimFundsFn).toHaveBeenCalledWith(mockLoginToken, {
          id: 'mock-fulfillment-id',
          paymentMethod: method,
          ...claimInputs[mutation],
        });
      },
    );

    describe('issueFunds', () => {
      const resolver = fulfillmentResolvers.FulfillmentMutations.issueFunds;

      it('throws error if user is not an admin', async () => {
        await expect(
          resolver(
            undefined,
            { input: { id: 'mock-fulfillment-id' } },
            {
              token: mockLoginToken,
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow('User is not an admin');
      });

      it('throws error if the fulfillment is in a different partner', async () => {
        await expect(
          resolver(
            undefined,
            { input: { id: 'mock-fulfillment-id' } },
            {
              services: {
                admins: { findById: vi.fn().mockResolvedValueOnce({}) },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
                fulfillments: {
                  findWithOptions: vi.fn().mockResolvedValueOnce({
                    case: { program: { partnerId: 'other-partner-id' } },
                  }),
                },
              },
              token: mockAdminToken,
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow('User is trying to take action on an entity in a different partner');
      });

      it('throws error if the fulfillment does not exist', async () => {
        await expect(
          resolver(
            undefined,
            { input: { id: 'mock-fulfillment-id' } },
            {
              services: {
                admins: { findById: vi.fn().mockResolvedValueOnce({}) },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
                fulfillments: {
                  findWithOptions: vi.fn(),
                },
              },
              token: mockAdminToken,
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow('User is trying to take action on an entity in a different partner');
      });

      it('throws error if Payments: Partner Issued is not enabled', async () => {
        await expect(
          resolver(
            undefined,
            { input: { id: 'mock-fulfillment-id' } },
            {
              services: {
                admins: { findById: vi.fn().mockResolvedValueOnce({}) },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
                fulfillments: {
                  findWithOptions: vi.fn().mockResolvedValueOnce({
                    case: { program: { partnerId: mockAdminToken.partnerId } },
                  }),
                },
                programFeatures: { hasFeatures: vi.fn().mockResolvedValueOnce(false) },
              },
              token: mockAdminToken,
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow('All features Payments: Partner Issued are not enabled');
      });

      it('calls FulfillmentService.issueFunds if all conditions pass', async () => {
        const mockIssueFundsFn = vi.fn().mockResolvedValueOnce({ metadata: { status: 200 } });
        const response = await resolver(
          undefined,
          { input: { id: 'mock-fulfillment-id' } },
          {
            operations: { payments: { issueFunds: { run: mockIssueFundsFn } } },
            services: {
              admins: { findById: vi.fn().mockResolvedValueOnce({}) },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              fulfillments: {
                findWithOptions: vi.fn().mockResolvedValueOnce({
                  case: { program: { partnerId: mockAdminToken.partnerId } },
                }),
              },
              programFeatures: { hasFeatures: vi.fn().mockResolvedValueOnce(true) },
            },
            token: mockAdminToken,
          } as unknown as AdminContext,
          {} as GraphQLResolveInfo,
        );
        expect(response).toEqual({ metadata: { status: 200 } });
        expect(mockIssueFundsFn).toHaveBeenCalledWith(mockAdminToken, {
          id: 'mock-fulfillment-id',
        });
      });
    });

    describe('approveAmount', () => {
      const resolver = fulfillmentResolvers.FulfillmentMutations.approveAmount;

      it('throws error if user is not an admin', async () => {
        await expect(
          resolver(
            undefined,
            { input: { id: 'mock-fulfillment-id' } },
            {
              token: mockLoginToken,
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow('User is not an admin');
      });

      it('throws error if the fulfillment is in a different partner', async () => {
        await expect(
          resolver(
            undefined,
            { input: { id: 'mock-fulfillment-id' } },
            {
              services: {
                admins: { findById: vi.fn().mockResolvedValueOnce({}) },
                identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
                fulfillments: {
                  findWithOptions: vi.fn().mockResolvedValueOnce({
                    case: { program: { partnerId: 'other-partner-id' } },
                  }),
                },
              },
              token: mockAdminToken,
            } as unknown as AdminContext,
            {} as GraphQLResolveInfo,
          ),
        ).rejects.toThrow('User is trying to take action on an entity in a different partner');
      });

      it('calls ApprovePayments operation if all conditions pass', async () => {
        const mockApprovePaymentsFn = vi.fn().mockResolvedValueOnce({ metadata: { status: 200 } });
        const response = await resolver(
          undefined,
          { input: { id: 'mock-fulfillment-id' } },
          {
            operations: {
              cases: {
                approvePayments: { run: mockApprovePaymentsFn },
              },
            },
            services: {
              admins: { findById: vi.fn().mockResolvedValueOnce({}) },
              identity: { checkPermission: vi.fn().mockResolvedValueOnce({ canAccess: true }) },
              fulfillments: {
                findById: vi.fn().mockResolvedValueOnce({ caseId: 'mock-case-id' }),
                findWithOptions: vi.fn().mockResolvedValueOnce({
                  case: { program: { partnerId: mockAdminToken.partnerId } },
                }),
              },
              programFeatures: { hasFeatures: vi.fn().mockResolvedValueOnce(true) },
            },
            token: mockAdminToken,
          } as unknown as AdminContext,
          {} as GraphQLResolveInfo,
        );
        expect(response).toEqual({ metadata: { status: 200 } });
        expect(mockApprovePaymentsFn).toHaveBeenCalledWith(mockAdminToken, {
          ids: ['mock-case-id'],
        });
      });
    });
  });
});
