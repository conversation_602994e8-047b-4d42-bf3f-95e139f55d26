enum ScheduleType {
  onetime
  recurring
}

type Fulfillment {
  id: UUID!
  displayId: String!
  case: Case!
  fund: Fund!
  payments: [Payment!]
  fulfillmentMeta: FulfillmentMeta
  approvedAmount: NonNegativeInt!
  scheduleType: ScheduleType!
  schedule: [DateTime!]
  paymentPattern: PaymentPattern
}

input FulfillmentFilter {
  ids: [UUID!]
}

type FulfillmentPage {
  fulfillments: [Fulfillment!]!
  pageInfo: PageInfo!
}

input ClaimDirectDepositInput {
  id: UUID!
  bankAccount: BankAccountInput!
}

input ClaimCheckInput {
  id: UUID!
  address: CreateAddressInput!
}

input ClaimPhysicalCardInput {
  id: UUID!
  address: CreateAddressInput!
}

input ClaimVirtualCardInput {
  id: UUID!
  address: CreateAddressInput!
}

input ClaimZelleInput {
  id: UUID!
}

input IssueFundsInput {
  id: UUID!
}

type ClaimFulfillmentResponse {
  metadata: ResponseMetadata!
  record: Fulfillment
  query: Query!
}

type FulfillmentResponse {
  metadata: ResponseMetadata!
  record: Fulfillment
  query: Query!
}

input ApproveAmountInput {
  id: UUID!
}

# Upcoming for bulk
input BulkIssueFundsInput {
  ids: [UUID!]!
}

type BulkFulfillmentResponse {
  metadata: BulkResponseMetadata!
  records: [Fulfillment!]
}

type FulfillmentMutations {
  claimCheck(input: ClaimCheckInput!): ClaimFulfillmentResponse!
  claimDirectDeposit(input: ClaimDirectDepositInput!): ClaimFulfillmentResponse!
  claimPhysicalCard(input: ClaimPhysicalCardInput!): ClaimFulfillmentResponse!
  claimVirtualCard(input: ClaimVirtualCardInput!): ClaimFulfillmentResponse!
  claimZelle(input: ClaimZelleInput!): ClaimFulfillmentResponse!
  issueFunds(input: IssueFundsInput!): ClaimFulfillmentResponse!
  bulkIssueFunds(input: BulkIssueFundsInput): BulkFulfillmentResponse!

  approveAmount(input: ApproveAmountInput): FulfillmentResponse!
    @deprecated(reason: "Replaced with case.approvePayments")
}
