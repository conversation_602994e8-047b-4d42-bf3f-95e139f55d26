type Month {
  month: NonNegativeInt!
  year: NonNegativeInt!
}

enum SubBenefitType {
  Arrearage
  Prospective
}

enum BenefitType {
  Rental
  Utility
  Other
}

type FulfillmentMeta {
  id: UUID!
  months: [Month!]!
  benefit: BenefitType
  type: SubBenefitType
  serviceDate: Date
  startDate: Date
  endDate: Date
  accountNumber: String
  utilityType: UtilityType
  expenseType: ExpenseType
  checkNumber: String
  billingCode: String
  fulfillment: Fulfillment!
}
