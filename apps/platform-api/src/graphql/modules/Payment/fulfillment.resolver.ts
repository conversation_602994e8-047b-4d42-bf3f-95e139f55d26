import {
  Fulfillment,
  ClaimCheckRequest,
  ClaimDirectDepositRequest,
  ClaimPhysicalCardRequest,
  ClaimVirtualCardRequest,
  ClaimZelleRequest,
  ApproveAmountInput,
  FeatureName,
  PaymentMethod,
} from '@bybeam/platform-types';
import { ObjectType, Relation, Permission } from '@bybeam/identity-client/types';
import { ResolversComposition, composeResolvers } from '@graphql-tools/resolvers-composition';
import { AdminContext, AuthenticatedContext } from '../../../@types/graphql.js';
import HasPermission from '../../../resolvers/composition/HasPermission.js';
import HasProgramFeatures from '../../../resolvers/composition/HasProgramFeatures.js';
import InSamePartner from '../../../resolvers/composition/InSamePartner.js';
import IsAdmin from '../../../resolvers/composition/IsAdmin.js';
import IsAuthenticated from '../../../resolvers/composition/IsAuthenticated.js';
import IsOwner from '../../../resolvers/composition/IsOwner.js';
import { GraphQLResolveInfo, GraphQLFieldResolver } from 'graphql';
import { IResolvers } from '@graphql-tools/utils';
import { In } from 'typeorm';
import { AuthenticationError } from '../../../@types/errors.js';

// --- Types ---
type FulfillmentFieldResolver<ContextType, ReturnType> = (
  parent: Fulfillment,
  args: Record<string, unknown>,
  context: ContextType,
  info: GraphQLResolveInfo,
) => Promise<ReturnType>;

type FulfillmentMutations = {
  claimCheck: GraphQLFieldResolver<void, AuthenticatedContext, { input: ClaimCheckRequest }>;
  claimDirectDeposit: GraphQLFieldResolver<
    void,
    AuthenticatedContext,
    { input: ClaimDirectDepositRequest }
  >;
  claimPhysicalCard: GraphQLFieldResolver<
    void,
    AuthenticatedContext,
    { input: ClaimPhysicalCardRequest }
  >;
  claimVirtualCard: GraphQLFieldResolver<
    void,
    AuthenticatedContext,
    { input: ClaimVirtualCardRequest }
  >;
  claimZelle: GraphQLFieldResolver<void, AuthenticatedContext, { input: ClaimZelleRequest }>;
  issueFunds: GraphQLFieldResolver<void, AdminContext, { input: { id: string } }>;
  bulkIssueFunds: GraphQLFieldResolver<void, AdminContext, { input: { ids: string[] } }>;
  approveAmount: GraphQLFieldResolver<void, AdminContext, { input: ApproveAmountInput }>;
};

type FulfillmentResolvers = IResolvers<Fulfillment, AuthenticatedContext> & {
  Mutation: {
    fulfillment: () => { _type: 'FulfillmentMutations' };
  };
  FulfillmentMutations: FulfillmentMutations;
  Fulfillment: {
    case: FulfillmentFieldResolver<AdminContext, unknown>;
    fund: FulfillmentFieldResolver<AdminContext, unknown>;
    fulfillmentMeta: FulfillmentFieldResolver<AdminContext, unknown>;
    paymentPattern: FulfillmentFieldResolver<AuthenticatedContext, unknown>;
    schedule: FulfillmentFieldResolver<AuthenticatedContext, unknown>;
  };
};

// --- Fulfillment Resolvers ---
const fulfillmentResolvers: FulfillmentResolvers = {
  Mutation: {
    fulfillment: () => ({ _type: 'FulfillmentMutations' }),
  },
  FulfillmentMutations: {
    claimCheck: async (_, { input }: { input: ClaimCheckRequest }, context: AuthenticatedContext) =>
      context.operations.payments.claimFunds.run(context.token, {
        ...input,
        paymentMethod: PaymentMethod.Check,
      }),
    claimDirectDeposit: async (
      _,
      { input }: { input: ClaimDirectDepositRequest },
      context: AuthenticatedContext,
    ) =>
      context.operations.payments.claimFunds.run(context.token, {
        ...input,
        paymentMethod: PaymentMethod.DirectDeposit,
      }),
    claimPhysicalCard: async (
      _,
      { input }: { input: ClaimPhysicalCardRequest },
      context: AuthenticatedContext,
    ) =>
      context.operations.payments.claimFunds.run(context.token, {
        ...input,
        paymentMethod: PaymentMethod.PhysicalCard,
      }),
    claimVirtualCard: async (
      _,
      { input }: { input: ClaimVirtualCardRequest },
      context: AuthenticatedContext,
    ) =>
      context.operations.payments.claimFunds.run(context.token, {
        ...input,
        paymentMethod: PaymentMethod.VirtualCard,
      }),
    claimZelle: async (_, { input }: { input: ClaimZelleRequest }, context: AuthenticatedContext) =>
      context.operations.payments.claimFunds.run(context.token, {
        ...input,
        paymentMethod: PaymentMethod.Zelle,
      }),
    issueFunds: async (_, { input }: { input: { id: string } }, context: AdminContext) =>
      context.operations.payments.issueFunds.run(context.token, input),
    bulkIssueFunds: async (_, { input }: { input: { ids: string[] } }, context: AdminContext) =>
      context.operations.payments.bulkIssueFunds.run(context.token, input),
    approveAmount: async (_, { input }: { input: ApproveAmountInput }, context: AdminContext) => {
      const fulfillment = await context.services.fulfillments.findById(input.id);
      return context.operations.cases.approvePayments.run(context.token, {
        ids: [fulfillment.caseId],
      });
    },
  },
  Fulfillment: {
    case: async ({ caseId }: Fulfillment, _: unknown, context: AdminContext) =>
      context.services.cases.findById(caseId),
    fund: async ({ fundId }: Fulfillment, _: unknown, context: AdminContext) =>
      context.services.funds.findById(fundId),
    fulfillmentMeta: async (fulfillment: Fulfillment, _: unknown, context: AdminContext) =>
      fulfillment.fulfillmentMeta ??
      (await context.services.fulfillmentMeta.findByFulfillmentId(fulfillment.id)),
    paymentPattern: async (fulfillment: Fulfillment, _: unknown, context: AuthenticatedContext) =>
      fulfillment.paymentPattern ??
      (await context.services.paymentPatterns.findByFulfillmentId(fulfillment.id)),
    schedule: async (fulfillment: Fulfillment, _: unknown, context: AuthenticatedContext) =>
      context.services.fulfillments.getPaymentSchedule(fulfillment.id),
  },
};

// --- Compositions ---
const FulfillmentInSamePartner = <Input extends { id: string }>(): ResolversComposition =>
  InSamePartner<{ input: Input }>(async (args, context) => {
    const fulfillment = await context.services.fulfillments.findWithOptions({
      where: { id: args.input.id },
      relations: ['case', 'case.program'],
    });
    return fulfillment?.case?.program?.partnerId ?? '';
  });

const UserIsPayee = <Input extends { id: string }>(): ResolversComposition[] => [
  IsOwner<{ input: Input }>(
    async (args, context) =>
      (
        await context.services.fulfillments.findWithOptions({
          where: { id: args.input.id },
          relations: ['payments'],
        })
      )?.payments?.[0]?.payeeId === context.token.userId,
  ),
  HasPermission<void, { input: Input }>({
    resourceRef: {
      getIdentifier: async (_, { input }) => input.id,
      objectType: ObjectType.FULFILLMENT,
    },
    action: Relation.Payee,
  }),
];

const FulfillmentAreFeaturesEnabled = <Input extends { id: string }>(
  features: FeatureName[],
): ResolversComposition =>
  HasProgramFeatures<{ input: Input }>(
    async (args, context) =>
      (
        await context.services.fulfillments.findWithOptions({
          where: { id: args.input.id },
          relations: ['case'],
        })
      )?.case?.programId ?? '',
    features,
  );

const FulfillmentsInSamePartner = <Input extends { ids: string[] }>() =>
  InSamePartner<{ input: Input }>(async (args, context) => {
    return (
      await context.services.fulfillments.findManyWithOptions({
        where: { id: In(args.input.ids) },
        relations: ['case', 'case.program'],
      })
    ).map((fulfillment) => fulfillment?.case?.program?.partnerId ?? '');
  });

const AllProgramsHasFeatures =
  (): ResolversComposition => (next) => async (root, args, context: AdminContext, info) => {
    const programIds = (
      await context.services.fulfillments.findManyWithOptions({
        where: { id: In(args.input.ids) },
        relations: ['case'],
      })
    ).map((fulfillment) => fulfillment?.case?.programId ?? '');
    const hasFeatures = (
      await Promise.all(
        programIds.map((id) =>
          context.services.programFeatures.hasFeature({
            programId: id,
            feature: FeatureName.PaymentsPartnerIssued,
          }),
        ),
      )
    ).every(Boolean);
    if (!hasFeatures) throw new AuthenticationError('PaymentsPartnerIssued is not enabled');
    return next(root, args, context, info);
  };

const resolverComposition = {
  'FulfillmentMutations.claimCheck': [
    IsAuthenticated(),
    ...UserIsPayee(),
    FulfillmentInSamePartner(),
    FulfillmentAreFeaturesEnabled([FeatureName.PaymentsClaimFunds, FeatureName.PaymentsCheck]),
  ],
  'FulfillmentMutations.claimDirectDeposit': [
    IsAuthenticated(),
    ...UserIsPayee(),
    FulfillmentInSamePartner(),
    FulfillmentAreFeaturesEnabled([
      FeatureName.PaymentsClaimFunds,
      FeatureName.PaymentsDirectDeposit,
    ]),
  ],
  'FulfillmentMutations.claimPhysicalCard': [
    IsAuthenticated(),
    ...UserIsPayee(),
    FulfillmentInSamePartner(),
    FulfillmentAreFeaturesEnabled([
      FeatureName.PaymentsClaimFunds,
      FeatureName.PaymentsPrepaidCard,
    ]),
  ],
  'FulfillmentMutations.claimVirtualCard': [
    IsAuthenticated(),
    ...UserIsPayee(),
    FulfillmentInSamePartner(),
    FulfillmentAreFeaturesEnabled([
      FeatureName.PaymentsClaimFunds,
      FeatureName.PaymentsPrepaidCard,
    ]),
  ],
  'FulfillmentMutations.claimZelle': [
    IsAuthenticated(),
    ...UserIsPayee(),
    FulfillmentInSamePartner(),
    FulfillmentAreFeaturesEnabled([FeatureName.PaymentsClaimFunds, FeatureName.PaymentsZelle]),
  ],
  'FulfillmentMutations.issueFunds': [
    IsAdmin(Permission.Fiscal),
    FulfillmentInSamePartner(),
    FulfillmentAreFeaturesEnabled([FeatureName.PaymentsPartnerIssued]),
  ],
  'FulfillmentMutations.bulkIssueFunds': [
    IsAdmin(Permission.Fiscal),
    FulfillmentsInSamePartner(),
    AllProgramsHasFeatures(),
  ],
  'FulfillmentMutations.approveAmount': [IsAdmin(), FulfillmentInSamePartner()],
  'Fulfillment.case': [],
  'Fulfillment.fund': [],
  'Fulfillment.fulfillmentMeta': [],
  'Fulfillment.paymentPattern': [],
  'Fulfillment.schedule': [],
};

export default composeResolvers(fulfillmentResolvers, resolverComposition);
