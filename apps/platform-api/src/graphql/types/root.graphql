type Query {
  accessRequests(filter: AccessRequestFilter, pagination: OffsetPagination): AccessRequestPage!
  aggregateApplication(filter: AggregateApplicationFilter): AggregateApplication!
  aggregatePayment(filter: AggregatePaymentFilter): AggregatePayment!
  applications(filter: ApplicationFilter, pagination: CursorPagination): ApplicationPage!
  cases(filter: CaseFilter, pagination: OffsetPagination, sort: CaseSort): CasePage!
  changelogs: [Changelog!]!
  fulfillments(filter: FulfillmentFilter, pagination: OffsetPagination): FulfillmentPage!
  hasAccess(input: HasAccessInput!): HasAccessResponse!
  incomeLimitAreas(
    filter: IncomeLimitAreaFilter
    pagination: CursorPagination
  ): IncomeLimitAreaPage!
  partners(filter: PartnerFilter, pagination: CursorPagination): PartnerPage!
  payments(vendorId: UUID!, pagination: OffsetPagination, sort: PaymentSort): PaymentPage!
  users(filter: User<PERSON>ilter, pagination: OffsetPagination, sort: UserSort): UserPage!
  currentUser: User
}

type Mutation {
  accessRequest: AccessRequestMutations
  application: ApplicationMutations
  case: CaseMutations
  document: DocumentMutations
  enrollment: EnrollmentMutations
  fulfillment: FulfillmentMutations
  identity: IdentityMutations
  note: NoteMutations
  payment: PaymentMutations
  preset: PresetMutations
  savedView: SavedViewMutations
  tag: TagMutations
  user: UserMutations
  vendor: VendorMutations
}

type ResponseMetadata {
  id: UUID
  status: NonNegativeInt!
  message: String
  errors: [String]
}

type BulkResponseError {
  id: UUID!
  message: NonEmptyString!
}

type BulkResponseMetadata {
  ids: [UUID!]
  status: NonNegativeInt!
  message: NonEmptyString
  errors: [BulkResponseError!]
}
