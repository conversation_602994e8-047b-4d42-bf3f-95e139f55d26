import { ApplicationContext, ApplicationState } from '@payments/@types/index.js';
import Application from 'koa';
import Router from 'koa-router';
import { decodeBase64 } from '../utilities/cryptography.js';
import { logger } from '../utilities/logger/index.js';
import { StatusCodes } from 'http-status-codes';

function parseMessageBody<T>(request: Application.Request): T {
  const {
    message: { data },
  } = request.body;
  // TODO: Should we just send the message unwrapped so we don't have to decode it here?
  return JSON.parse(decodeBase64(data)) as T;
}

export default function subscriptionRoutes(app: Application<ApplicationState, ApplicationContext>) {
  const router = new Router({ prefix: '/v1/subscriptions' });

  router.post('/scheduledPayments', async (ctx, next) => {
    ctx.state.response = await app.context.services.schedules.processPayment(
      parseMessageBody(ctx.request),
    );
    return next();
  });

  router.post('/transfers', async (ctx, next) => {
    ctx.state.response = await app.context.services.transfers.handleMessage(
      parseMessageBody(ctx.request),
    );
    return next();
  });

  router.post('/processPayment', async (ctx, next) => {
    logger.info({ body: ctx.request.body }, 'subscription message received ...');
    ctx.state.response = { status: StatusCodes.OK };
    return next();
  });

  return router;
}
